---
description: 
globs:
alwaysApply: true
---
# terminal-service-pattern

这是一个规则文件，用于帮助 AI 理解您的代码库和遵循项目约定。
# 终端服务开发模式

## 终端处理器架构
- 每个供应商有独立的处理器实现
- 处理器继承`AbstractProviderTerminalHandler`
- 使用工厂模式根据供应商类型创建处理器
- 处理器负责具体的终端绑定、查询等操作

## 支持的供应商
- 海科（Haike）：`HaikeProviderTerminalHandler`
- 华兴银行（HXB）：`HxbProviderTerminalHandler`
- 联智宝（LZB）：`LzbProviderTerminalHandler`
- 通联V2（TongLianV2）：`TongLianV2ProviderTerminalHandler`
- UMB：`UmbProviderTerminalHandler`

## 数据模型
- 终端信息：`ProviderTerminalDO`
- 终端任务：`ProviderTerminalTaskDO`
- 商户参数：`MerchantProviderParamsDO`
- 渠道信息：`McChannelDO`
- 合同任务：`ContractTaskDO`

## 任务管理
- 任务状态枚举：`ProviderTerminalTaskStatusEnum`
- 任务类型枚举：`ProviderTerminalTaskTypeEnum`
- 异步任务处理机制
- 任务重试和失败处理

## 外部服务集成
- 核心业务系统（CoreB）：`CorebClient`
- 商户合同服务：`MerchantContractClient`
- 统一的RPC调用配置

## 配置管理
- 使用Apollo配置中心
- 环境配置：dev、test、beta、prod
- 数据源配置：`MyBatisPlusConfig`
- RPC服务配置：`InternalRpcServiceConfig`

## 监控和日志
- 统一日志记录：`JsonRpcLogAspect`
- 错误处理：`JsonRpcErrorResolver`
- 健康检查：`HealthyController`
- 指标监控：`MetricsController`
description:
globs:
alwaysApply: false
---