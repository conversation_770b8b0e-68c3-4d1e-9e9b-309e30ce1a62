# 项目结构指南

这是一个收钱吧（Shouqianba）的终端服务项目，采用Maven多模块架构。

## 项目模块
- **provider-terminal-service-api**: API模块，包含DTO、枚举、异常等公共定义
- **provider-terminal-service-core**: 核心业务模块，包含主要的业务逻辑实现

## 主要入口点
主应用程序入口：[ProviderTerminalServiceApplication.java](mdc:provider-terminal-service-core/src/main/java/com/shouqianba/cua/terminal/core/ProviderTerminalServiceApplication.java)

## 核心包结构
- `com.shouqianba.cua.terminal.api`: API定义和DTO
- `com.shouqianba.cua.terminal.core`: 核心业务逻辑
  - `controller`: 控制器层
  - `service`: 服务层
  - `dao`: 数据访问层
  - `handler`: 处理器层（不同供应商的终端处理）
  - `model`: 数据模型
  - `config`: 配置类
  - `externalservice`: 外部服务调用

## 技术栈
- Spring Boot 2.7.18
- Java 17
- Maven
- Apollo配置中心
- MyBatis Plus
description:
globs:
alwaysApply: false
---
