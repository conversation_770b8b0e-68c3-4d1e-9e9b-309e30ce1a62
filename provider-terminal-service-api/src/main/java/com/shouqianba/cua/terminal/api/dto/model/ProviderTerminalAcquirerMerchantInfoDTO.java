package com.shouqianba.cua.terminal.api.dto.model;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/8/6
 */
@Data
@Accessors(chain = true)
public class ProviderTerminalAcquirerMerchantInfoDTO {

    /**
     * 收单机构商户号
     */
    @NotBlank(message = "收单机构商户号不能为空")
    private String acquirerMerchantId;
    /**
     * 收单机构商户号对应的收钱吧商户号
     */
    @NotBlank(message = "收单机构商户号对应的收钱吧商户号不能为空")
    private String merchantSn;

    /**
     * 收单机构通道
     */
    @NotNull(message = "通道不能为空")
    private ProviderEnum provider;
}
