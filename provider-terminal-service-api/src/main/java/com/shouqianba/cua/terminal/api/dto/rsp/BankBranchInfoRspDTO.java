package com.shouqianba.cua.terminal.api.dto.rsp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 根据银行名分支行信息
 */
@Data
public class BankBranchInfoRspDTO {

    @JsonProperty("bank_branch_list")
    private List<BankBranchInfo> bankBranchInfoList;

    @Data
    public static class BankBranchInfo {
        /**
         * 分支行id
         */
        @JsonProperty("id")
        private String id;

        /**
         * 开户行号
         */
        @JsonProperty("opening_number")
        private String openingNumber;

        /**
         * 清算行号
         */
        @JsonProperty("clearing_number")
        private String clearingNumber;

        /**
         * 银行名称
         */
        @JsonProperty("bank_name")
        private String bankName;

        /**
         * 分支行名称
         */
        @JsonProperty("branch_name")
        private String branchName;

        /**
         * 银行城市code
         */
        @JsonProperty("bank_city_code")
        private String bankCityCode;
    }
}
