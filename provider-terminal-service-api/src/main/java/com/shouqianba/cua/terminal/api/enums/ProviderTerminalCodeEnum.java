package com.shouqianba.cua.terminal.api.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * <AUTHOR>
 * @date 2025/8/6
 */
public enum ProviderTerminalCodeEnum implements ITextValueEnum<Integer> {
    SUCCESS(200, "操作成功"),
    ILLEGAL_ARGUMENT(422, "入参错误"),
    SERVER_ERROR(500, "系统异常");

    private final Integer value;
    private final String text;

    ProviderTerminalCodeEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }
}
