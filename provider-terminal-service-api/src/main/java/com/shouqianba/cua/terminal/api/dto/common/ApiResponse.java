package com.shouqianba.cua.terminal.api.dto.common;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.shouqianba.cua.terminal.api.enums.ProviderTerminalCodeEnum;
import lombok.Data;

/**
 * 统一API响应包装类
 *
 * @param <T> 响应数据类型
 * <AUTHOR>
 * @date 2025/6/20 16:30
 */
@Data
public class ApiResponse<T> {

    /**
     * 响应状态码 200-成功 400-客户端错误 500-服务器错误 600-业务错误
     */
    @JsonProperty("code")
    private Integer code;

    /**
     * 响应消息
     */
    @JsonProperty("message")
    private String message;

    /**
     * 响应数据
     */
    @JsonProperty("data")
    private T data;

    /**
     * 响应时间戳
     */
    @JsonProperty("timestamp")
    private Long timestamp;

    /**
     * 是否成功（用于其他系统判断）
     */
    @JsonProperty("success")
    private Boolean success;

    /**
     * 私有构造函数
     */
    private ApiResponse() {
        this.timestamp = System.currentTimeMillis();
        this.success = false; // 默认失败
    }

    /**
     * 私有构造函数
     */
    private ApiResponse(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.timestamp = System.currentTimeMillis();
        this.success = ProviderTerminalCodeEnum.SUCCESS.getValue().equals(code);
    }

    /**
     * 成功响应（无数据）
     *
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(ProviderTerminalCodeEnum.SUCCESS.getValue(), ProviderTerminalCodeEnum.SUCCESS.getText(), null);
    }

    /**
     * 成功响应（带数据）
     *
     * @param data 响应数据
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(ProviderTerminalCodeEnum.SUCCESS.getValue(), ProviderTerminalCodeEnum.SUCCESS.getText(), data);
    }

    /**
     * 成功响应（带自定义消息和数据）
     *
     * @param message 响应消息
     * @param data    响应数据
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(ProviderTerminalCodeEnum.SUCCESS.getValue(), message, data);
    }

    /**
     * 失败响应（使用响应码枚举）
     *
     * @param ProviderTerminalCodeEnum 响应码枚举
     * @return 失败响应
     */
    public static <T> ApiResponse<T> fail(ProviderTerminalCodeEnum ProviderTerminalCodeEnum) {
        return new ApiResponse<>(ProviderTerminalCodeEnum.getValue(), ProviderTerminalCodeEnum.getText(), null);
    }

    /**
     * 失败响应（使用响应码枚举和自定义消息）
     *
     * @param ProviderTerminalCodeEnum 响应码枚举
     * @param message                  自定义错误消息
     * @return 失败响应
     */
    public static <T> ApiResponse<T> fail(ProviderTerminalCodeEnum ProviderTerminalCodeEnum, String message) {
        return new ApiResponse<>(ProviderTerminalCodeEnum.getValue(), message, null);
    }

    /**
     * 失败响应（使用响应码枚举、自定义消息和数据）
     *
     * @param ProviderTerminalCodeEnum 响应码枚举
     * @param message                  自定义错误消息
     * @param data                     响应数据
     * @return 失败响应
     */
    public static <T> ApiResponse<T> fail(ProviderTerminalCodeEnum ProviderTerminalCodeEnum, String message, T data) {
        return new ApiResponse<>(ProviderTerminalCodeEnum.getValue(), message, data);
    }

    /**
     * 失败响应（使用自定义状态码和消息）
     *
     * @param code    状态码
     * @param message 错误消息、
     * @return 失败响应
     */
    public static <T> ApiResponse<T> fail(Integer code, String message) {
        return new ApiResponse<>(code, message, null);
    }

    /**
     * 判断是否成功
     *
     * @return 是否成功
     */
    @JsonIgnore
    public boolean isSuccess() {
        return ProviderTerminalCodeEnum.SUCCESS.getValue().equals(this.code);
    }

    /**
     * 判断是否失败
     *
     * @return 是否失败
     */
    @JsonIgnore
    public boolean isFailure() {
        return !isSuccess();
    }
} 