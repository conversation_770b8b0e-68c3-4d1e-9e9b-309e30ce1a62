package com.shouqianba.cua.terminal.api.exception;

import com.shouqianba.cua.terminal.api.enums.ProviderTerminalCodeEnum;
import com.wosai.common.exception.CommonException;

/**
 * 支付业务异常类
 * 继承CommonException，支持传入异常枚举
 *
 * <AUTHOR>
 * @date 2025/1/13 16:00
 */
public class ProviderTerminalBizException extends CommonException {

    private int code;
    private String msg;

    public ProviderTerminalBizException(String msg) {
        super(msg);
    }

    public ProviderTerminalBizException(int code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public ProviderTerminalBizException(Throwable throwable) {
        super(throwable.getMessage());
        this.code = ProviderTerminalCodeEnum.SERVER_ERROR.getValue();
        this.msg = throwable.getMessage();
    }

    @Override
    public int getCode() {
        return this.code;
    }

    public ProviderTerminalBizException(ProviderTerminalCodeEnum respCode) {
        super(respCode.getText());
        this.code = respCode.getValue();
        this.msg = respCode.getText();
    }

    public ProviderTerminalBizException(ProviderTerminalCodeEnum respCode, String msg) {
        super(msg);
        this.code = respCode.getValue();
        this.msg = msg;
    }

} 