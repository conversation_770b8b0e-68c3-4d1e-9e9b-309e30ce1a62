server.tomcat.mbeanregistry.enabled=true
spring.datasource.url=
spring.datasource.driverClassName=com.mysql.jdbc.Driver
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=30
spring.datasource.hikari.connection-test-query=select 1 from dual


internal.service.core-business=http://app-core-business
internal.service.merchant-contract=http://merchant-contract