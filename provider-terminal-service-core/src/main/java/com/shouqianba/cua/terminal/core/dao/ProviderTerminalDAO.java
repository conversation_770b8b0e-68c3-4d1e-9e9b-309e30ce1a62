package com.shouqianba.cua.terminal.core.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shouqianba.cua.terminal.core.mapper.ProviderTerminalDynamicMapper;
import com.shouqianba.cua.terminal.core.model.entity.ProviderTerminalDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


/**
 * 收单机构终端表表数据库访问层 {@link ProviderTerminalDO}
 * 对ProviderTerminalMapper层做出简单封装 {@link ProviderTerminalDynamicMapper}
 */
@Repository
public class ProviderTerminalDAO extends AbstractBaseDAO<ProviderTerminalDO, ProviderTerminalDynamicMapper> {

    public ProviderTerminalDAO(SqlSessionFactory sqlSessionFactory, ProviderTerminalDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据商户号和收单机构对应的provider获取终端列表
     *
     * @param merchantSn 商户号
     * @param provider   收单机构对应的provider
     * @return 终端列表
     */
    public List<ProviderTerminalDO> listByMerchantSnAndProvider(String merchantSn, String provider) {
        LambdaQueryWrapper<ProviderTerminalDO> query = new LambdaQueryWrapper<>();
        query.eq(ProviderTerminalDO::getMerchantSn, merchantSn)
                .eq(ProviderTerminalDO::getProvider, provider);
        return entityMapper.selectList(query);
    }

    /**
     * 根据商户号和收单机构对应的provider删除终端参数
     *
     * @param merchantSn 商户号
     */
    public void deleteByMerchantSnAndProviders(String merchantSn, List<String> providers) {
        if (StringUtils.isBlank(merchantSn) || CollectionUtils.isEmpty(providers)) {
            return;
        }
        entityMapper.delete(new LambdaQueryWrapper<ProviderTerminalDO>()
                .eq(ProviderTerminalDO::getMerchantSn, merchantSn)
                .in(ProviderTerminalDO::getProvider, providers));
    }

    public void deleteTerminalLevelProviderTerminal(String merchantSn, String provider) {
        entityMapper.delete(new LambdaQueryWrapper<ProviderTerminalDO>()
                .eq(ProviderTerminalDO::getMerchantSn, merchantSn)
                .eq(ProviderTerminalDO::getProvider, provider)
                .isNotNull(ProviderTerminalDO::getTerminalSn));
    }

    public List<ProviderTerminalDO> listByProviderTerminalId(String providerTerminalId) {
        return entityMapper.selectList(new LambdaQueryWrapper<ProviderTerminalDO>()
                .eq(ProviderTerminalDO::getProviderTerminalId, providerTerminalId));
    }

    /**
     * 查询商户是否存在商户级别的终端信息
     *
     * @param provider 收单机构对应的provider
     * @param merchantSn 商户号
     * @param acquirerMerchantId 收单机构商户号
     * @return 存在返回终端信息，否则返回empty
     */
    public Optional<ProviderTerminalDO> getMerchantLevelProviderTerminal(Integer provider, String merchantSn, String acquirerMerchantId) {
        LambdaQueryWrapper<ProviderTerminalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProviderTerminalDO::getMerchantSn, merchantSn)
                .eq(ProviderTerminalDO::getProvider, provider)
                .eq(ProviderTerminalDO::getAcquirerMerchantId, acquirerMerchantId)
                .and(wrapper -> wrapper.isNull(ProviderTerminalDO::getStoreSn).or().eq(ProviderTerminalDO::getStoreSn, ""))
                .and(wrapper -> wrapper.isNull(ProviderTerminalDO::getTerminalSn).or().eq(ProviderTerminalDO::getTerminalSn, ""));
        
        return selectOne(queryWrapper);
    }

    /**
     * 查询是否存在终端级别的终端信息
     *
     * @param provider 收单机构对应的provider
     * @param merchantSn 商户号
     * @param storeSn 门店号
     * @param terminalSn 终端号
     * @param acquirerMerchantId 收单机构商户号
     * @return 存在返回终端信息，否则返回empty
     */
    public Optional<ProviderTerminalDO> getTerminalLevelProviderTerminal(Integer provider, String merchantSn, String storeSn, String terminalSn, String acquirerMerchantId) {
        LambdaQueryWrapper<ProviderTerminalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProviderTerminalDO::getMerchantSn, merchantSn)
                .eq(ProviderTerminalDO::getProvider, provider)
                .eq(ProviderTerminalDO::getStoreSn, storeSn)
                .eq(ProviderTerminalDO::getTerminalSn, terminalSn)
                .eq(ProviderTerminalDO::getAcquirerMerchantId, acquirerMerchantId);
        
        return selectOne(queryWrapper);
    }

    /**
     * 查询是否存在门店级别的终端信息
     *
     * @param provider 收单机构对应的provider
     * @param merchantSn 商户号
     * @param storeSn 门店号
     * @param acquirerMerchantId 收单机构商户号
     * @return 存在返回终端信息，否则返回empty
     */
    public Optional<ProviderTerminalDO> getStoreLevelProviderTerminal(Integer provider, String merchantSn, String storeSn, String acquirerMerchantId) {
        LambdaQueryWrapper<ProviderTerminalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProviderTerminalDO::getMerchantSn, merchantSn)
                .eq(ProviderTerminalDO::getProvider, provider)
                .eq(ProviderTerminalDO::getStoreSn, storeSn)
                .eq(ProviderTerminalDO::getAcquirerMerchantId, acquirerMerchantId)
                .and(wrapper -> wrapper.isNull(ProviderTerminalDO::getTerminalSn).or().eq(ProviderTerminalDO::getTerminalSn, ""));
        
        return selectOne(queryWrapper);
    }

}
