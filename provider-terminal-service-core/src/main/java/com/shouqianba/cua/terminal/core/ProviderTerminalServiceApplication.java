package com.shouqianba.cua.terminal.core;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.wosai.database.instrumentation.springboot.v2.EnableDataSourceTranslate;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@EnableApolloConfig
@SpringBootApplication
@EnableDataSourceTranslate
public class ProviderTerminalServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(ProviderTerminalServiceApplication.class, args);
    }

}
