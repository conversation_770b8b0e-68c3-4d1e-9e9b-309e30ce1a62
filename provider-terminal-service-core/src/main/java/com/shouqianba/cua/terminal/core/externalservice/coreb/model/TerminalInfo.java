package com.shouqianba.cua.terminal.core.externalservice.coreb.model;

import com.wosai.common.utils.WosaiMapUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
public class TerminalInfo {
    private String storeSn;
    private String vendorAppAppid;
    private String terminalSn;

    public TerminalInfo(Map terminalInfo) {
        this.storeSn = WosaiMapUtils.getString(terminalInfo, "store_sn");
        this.vendorAppAppid = WosaiMapUtils.getString(terminalInfo, "vendor_app_appid");
        this.terminalSn = WosaiMapUtils.getString(terminalInfo, "sn");
    }
}