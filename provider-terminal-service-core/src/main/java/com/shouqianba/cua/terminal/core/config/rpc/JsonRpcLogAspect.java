package com.shouqianba.cua.terminal.core.config.rpc;

import com.fasterxml.jackson.databind.JsonNode;
import com.googlecode.jsonrpc4j.InvocationListener;
import net.logstash.logback.marker.Markers;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * jsonRpc log
 */
@Component
public class JsonRpcLogAspect implements InvocationListener {

    private static final Logger log = LoggerFactory.getLogger(JsonRpcLogAspect.class);

    @Override
    public void willInvoke(Method method, List<JsonNode> arguments) {
        // do nothing yet
    }

    @Override
    public void didInvoke(Method method, List<JsonNode> arguments, Object result, Throwable t, long duration) {
        try {
            Map<String, Object> toAppendEntriesMap = new HashMap<>();
            toAppendEntriesMap.put("method", method.getDeclaringClass().getSimpleName() + "." + method.getName());
            toAppendEntriesMap.put("request", arguments);
            toAppendEntriesMap.put("duration", duration);
            toAppendEntriesMap.put("response", result);
            if (t != null) {
                toAppendEntriesMap.put("error", StringUtils.defaultIfEmpty(t.getMessage(), t.getCause().getMessage()));
                log.error(Markers.appendEntries(toAppendEntriesMap), "");
            } else {
                log.info(Markers.appendEntries(toAppendEntriesMap), "");
            }
        } catch (Exception e) {
            Map<String, Object> toAppendEntriesMap = new HashMap<>();
            toAppendEntriesMap.put("exception", e.getMessage());
            log.error(Markers.appendEntries(toAppendEntriesMap), "");
        }
    }
}
