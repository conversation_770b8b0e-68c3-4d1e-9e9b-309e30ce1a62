package com.shouqianba.cua.terminal.core.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.shouqianba.cua.terminal.core.mapper.MerchantProviderParamsDynamicMapper;
import com.shouqianba.cua.terminal.core.model.entity.MerchantProviderParamsDO;
import com.shouqianba.cua.terminal.core.model.enums.Deleted;
import com.wosai.common.utils.WosaiCollectionUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotBlank;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 商户报备获取的银行交易参数存储表(new)表数据库访问层 {@link MerchantProviderParamsDO}
 * 对MerchantProviderParamsMapper层做出简单封装 {@link MerchantProviderParamsDynamicMapper}
 */
@Repository
public class MerchantProviderParamsDAO extends AbstractBaseDAO<MerchantProviderParamsDO, MerchantProviderParamsDynamicMapper> {

    public MerchantProviderParamsDAO(SqlSessionFactory sqlSessionFactory, MerchantProviderParamsDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    public Optional<MerchantProviderParamsDO> getMerchantProviderParamsById(String id) {
        return Optional.ofNullable(entityMapper.selectById(id));
    }

    public Optional<MerchantProviderParamsDO> getMerchantProviderParamsByPayMerchantIdAndMerchantSn(String payMerchantId, String merchantSn) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MerchantProviderParamsDO::getPayMerchantId, payMerchantId);
        lambdaQueryWrapper.eq(MerchantProviderParamsDO::getMerchantSn, merchantSn);
        lambdaQueryWrapper.eq(MerchantProviderParamsDO::getDeleted, 0);
        return selectOne(lambdaQueryWrapper);
    }

    public Map<String, MerchantProviderParamsDO> getMerchantProviderParamsByPayMerchantIdList(List<String> payMerchantIdList) {
        if (WosaiCollectionUtils.isEmpty(payMerchantIdList)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(MerchantProviderParamsDO::getPayMerchantId, payMerchantIdList);
        lambdaQueryWrapper.eq(MerchantProviderParamsDO::getDeleted, 0);
        List<MerchantProviderParamsDO> merchantProviderParams = entityMapper.selectList(lambdaQueryWrapper);
        return merchantProviderParams.stream().collect(Collectors.toMap(MerchantProviderParamsDO::getPayMerchantId, v -> v));
    }

    public Optional<MerchantProviderParamsDO> getMerchantProviderParamsByProviderMerchantIdAndPayway(String providerMerchantId, Integer payway) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MerchantProviderParamsDO::getProviderMerchantId, providerMerchantId);
        lambdaQueryWrapper.eq(MerchantProviderParamsDO::getPayway, payway);
        return selectOne(lambdaQueryWrapper);
    }


    /**
     * 根据商户号,报备渠道,支付源获取交易参数
     *
     * @param merchantSn 商户号
     * @param channel    报备渠道
     * @param payWay     支付源
     * @return 交易参数
     */
    public Optional<MerchantProviderParamsDO> getMerChantProviderParams(String merchantSn, String channel, Integer payWay) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getChannelNo, channel)
                .eq(MerchantProviderParamsDO::getPayway, payWay)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .orderByDesc(MerchantProviderParamsDO::getCtime)
                .last("limit 1");
        return Optional.ofNullable(entityMapper.selectOne(lambdaQueryWrapper));
    }

    /**
     * 根据商户号,规则组id,报备规则,银行通道渠道号获取交易参数
     *
     * @param merchantSn   商户号
     * @param ruleGroupId  规则组id
     * @param contractRule 报备规则
     * @param channelNo    银行通道渠道号
     * @return 交易参数
     */
    public Optional<MerchantProviderParamsDO> getMerChantProviderParams(String merchantSn, String ruleGroupId, String contractRule, String channelNo) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getRuleGroupId, ruleGroupId)
                .eq(MerchantProviderParamsDO::getContractRule, contractRule)
                .eq(MerchantProviderParamsDO::getChannelNo, channelNo)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .orderByDesc(MerchantProviderParamsDO::getCtime)
                .last("limit 1");
        return Optional.ofNullable(entityMapper.selectOne(lambdaQueryWrapper));
    }

    public Optional<MerchantProviderParamsDO> getMerchantProviderParamsByProviderAndPayway(String merchantSn, Integer provider, Integer payway) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getProvider, provider)
                .eq(MerchantProviderParamsDO::getPayway, payway)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .orderByDesc(MerchantProviderParamsDO::getCtime)
                .last("limit 1");
        return Optional.ofNullable(entityMapper.selectOne(lambdaQueryWrapper));
    }

    public Optional<MerchantProviderParamsDO> getInUseProviderParams(String merchantSn, int payway) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getPayway, payway)
                .ne(MerchantProviderParamsDO::getProvider, payway)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .eq(MerchantProviderParamsDO::getStatus, 1)
                .orderByDesc(MerchantProviderParamsDO::getCtime)
                .last("limit 1");
        return Optional.ofNullable(entityMapper.selectOne(lambdaQueryWrapper));
    }

    /**
     * 根据商户号和配置状态,获取商户交易配置列表
     *
     * @param merchantSn 商户号
     * @param status     配置状态
     * @return 商户交易配置列表
     */
    public List<MerchantProviderParamsDO> listByMerchantSnAndStatus(String merchantSn, Integer status) {
        if (StringUtils.isBlank(merchantSn) || Objects.isNull(status)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getStatus, status)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据商户号,provider,payWay获取商户交易配置
     *
     * @param merchantSn 商户号
     * @param provider   渠道
     * @param payWay     支付源
     * @return 商户交易配置
     */
    public Optional<MerchantProviderParamsDO> getBySnAndProviderAndPayWay(String merchantSn, Integer provider, Integer payWay) {
        if (StringUtils.isBlank(merchantSn) || Objects.isNull(provider)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getProvider, provider)
                .eq(MerchantProviderParamsDO::getPayway, payWay)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                // 优先取已使用的配置
                .orderByDesc(MerchantProviderParamsDO::getStatus)
                .last("limit 1");
        return Optional.ofNullable(entityMapper.selectOne(lambdaQueryWrapper));
    }

    /**
     * 根据商户号和支付源获取商户交易配置
     *
     * @param merchantSn 商户号
     * @param payWay     支付源
     * @return 商户交易配置
     */
    public List<MerchantProviderParamsDO> listByMerchantSnAndPayWay(String merchantSn, Integer payWay) {
        if (StringUtils.isBlank(merchantSn)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getPayway, payWay)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据渠道商户号获取交易配置
     *
     * @param providerMerchantId 渠道商户号
     * @return 商户交易配置
     */
    public List<MerchantProviderParamsDO> listByProviderMerchantId(String providerMerchantId) {
        if (StringUtils.isBlank(providerMerchantId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getProviderMerchantId, providerMerchantId)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据商户号获取商户交易配置
     *
     * @param merchantSn 商户号
     * @return 商户交易配置
     */
    public List<MerchantProviderParamsDO> listByMerchantSnWithoutDeletedStatus(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn);
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据商户号获取商户交易配置
     *
     * @param merchantSn 商户号
     * @return 商户交易配置
     */
    public List<MerchantProviderParamsDO> listByMerchantSn(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据商户号获取已删除的商户交易配置
     *
     * @param merchantSn 商户号
     * @return 商户交易配置
     */
    public List<MerchantProviderParamsDO> listDeletedParamsByMerchantSn(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    public List<MerchantProviderParamsDO> listByIds(Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .in(MerchantProviderParamsDO::getId, ids)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    public List<MerchantProviderParamsDO> getByMerchantSnAndProviderAndPayWay(String merchantSn, Integer provider, Integer payWay) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getProvider, provider)
                .eq(MerchantProviderParamsDO::getPayway, payWay)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }


    /**
     * 根据商户号,payWay集合获取商户交易配置
     *
     * @param merchantSn 商户号
     * @param payWayList 支付源集合
     * @return 商户交易配置
     */
    public List<MerchantProviderParamsDO> getUsingBySnAndPayWayList(String merchantSn, List<Integer> payWayList) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .in(MerchantProviderParamsDO::getPayway, payWayList)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .eq(MerchantProviderParamsDO::getStatus, UseStatusEnum.IN_USE.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }


    /**
     * 根据商户号,provider,payWay集合获取商户交易配置
     *
     * @param merchantSn 商户号
     * @param payWayList 支付源集合
     * @param provider   通道
     * @return 商户交易配置
     */
    public List<MerchantProviderParamsDO> getParamsBySnAndPayWayListAndProvider(String merchantSn, List<Integer> payWayList, Integer provider) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .in(MerchantProviderParamsDO::getPayway, payWayList)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .eq(MerchantProviderParamsDO::getProvider, provider);
        return entityMapper.selectList(lambdaQueryWrapper);
    }
}
