package com.shouqianba.cua.terminal.core.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.shouqianba.cua.terminal.core.mapper.McChannelDynamicMapper;
import com.shouqianba.cua.terminal.core.model.entity.McChannelDO;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/23
 */
@Repository
public class McChannelDAO extends AbstractBaseDAO<McChannelDO, McChannelDynamicMapper> {

    protected McChannelDAO(SqlSessionFactory sqlSessionFactory, McChannelDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }


    public Optional<McChannelDO> getMcChannel(int payway, String provider, String channelNo) {
        LambdaQueryWrapper<McChannelDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(McChannelDO::getPayway, payway)
                .eq(McChannelDO::getProvider, provider)
                .eq(McChannelDO::getChannelNo, channelNo);
        return selectOne(queryWrapper);
    }

    public Optional<McChannelDO> getMcChannelByChannel(String channel) {
        LambdaQueryWrapper<McChannelDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(McChannelDO::getChannel, channel);
        return selectOne(queryWrapper);
    }

    public List<McChannelDO> getMcChannelByAcquirer(String acquirer, Integer payway) {
        LambdaQueryWrapper<McChannelDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(McChannelDO::getAcquirer, acquirer);
        queryWrapper.eq(McChannelDO::getPayway, payway);
        queryWrapper.eq(McChannelDO::getStatus, UseStatusEnum.IN_USE.getValue());
        return entityMapper.selectList(queryWrapper);
    }


    public List<McChannelDO> getMcChannelByPayway(Integer payway) {
        LambdaQueryWrapper<McChannelDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(McChannelDO::getPayway, payway);
        queryWrapper.eq(McChannelDO::getStatus, UseStatusEnum.IN_USE.getValue());
        return entityMapper.selectList(queryWrapper);
    }


}
