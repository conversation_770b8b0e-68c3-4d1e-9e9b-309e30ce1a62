package com.shouqianba.cua.terminal.core.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 62进制转换
 * <AUTHOR>
 * @Date: 2022/3/3 5:10 下午
 */
public class ScaleConverterUtil {

    private static String chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static String chars_36 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static int scale = 62;
    private static int scale_36 = 36;
    private static int minLength = 6;

    //数字转62进制
    public static String encode(long num) {
        StringBuilder sb = new StringBuilder();
        int remainder;
        while (num > scale - 1) {
            //对 scale 进行求余，然后将余数追加至 sb 中，由于是从末位开始追加的，因此最后需要反转字符串
            remainder = Long.valueOf(num % scale).intValue();
            sb.append(chars.charAt(remainder));
            //除以进制数，获取下一个末尾数
            num = num / scale;
        }
        sb.append(chars.charAt(Long.valueOf(num).intValue()));
        String value = sb.reverse().toString();
        return StringUtils.leftPad(value, minLength, '0');
    }

    public static String encode36(long num) {
        StringBuilder sb = new StringBuilder();
        int remainder;
        while (num > scale_36 - 1) {
            //对 scale 进行求余，然后将余数追加至 sb 中，由于是从末位开始追加的，因此最后需要反转字符串
            remainder = Long.valueOf(num % scale_36).intValue();
            sb.append(chars_36.charAt(remainder));
            //除以进制数，获取下一个末尾数
            num = num / scale_36;
        }
        sb.append(chars_36.charAt(Long.valueOf(num).intValue()));
        String value = sb.reverse().toString();
        return StringUtils.leftPad(value, minLength, '0');
    }

    //62进制转为数字
    public static long decode(String str) {
        //将 0 开头的字符串进行替换
        str = str.replace("^0*", "");
        long value = 0;
        char tempChar;
        int tempCharValue;
        for (int i = 0; i < str.length(); i++) {
            //获取字符
            tempChar = str.charAt(i);
            //单字符值
            tempCharValue = chars.indexOf(tempChar);
            //单字符值在进制规则下表示的值
            value += (long) (tempCharValue * Math.pow(scale, str.length() - i - 1));
        }
        return value;
    }


    //最大值为56800235583 超过这个62进制转换超6位
    public static void main(String[] args) {

//        Long aLong = Long.valueOf("00000000010");
//
//        System.out.println(aLong);
//
//        System.out.println(encode(aLong));
//


//        System.out.println(encode(100000000000081156L));
//        System.out.println(encode(00000000016L));


        System.out.println(encode(Long.parseLong("00000000016")));
        System.out.println(encode(16));
//        System.out.println(encode(56800235583L));
//        System.out.println(decode("zzzzzz"));
    }

}
