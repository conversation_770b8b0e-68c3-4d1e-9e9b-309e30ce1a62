package com.shouqianba.cua.terminal.core.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shouqianba.cua.terminal.core.mapper.ProviderTerminalTaskDynamicMapper;
import com.shouqianba.cua.terminal.core.model.entity.ProviderTerminalDO;
import com.shouqianba.cua.terminal.core.model.entity.ProviderTerminalTaskDO;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


/**
 * 收单机构终端报备任务表表数据库访问层 {@link ProviderTerminalTaskDO}
 * 对ProviderTerminalTaskMapper层做出简单封装 {@link ProviderTerminalTaskDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class ProviderTerminalTaskDAO extends AbstractBaseDAO<ProviderTerminalTaskDO, ProviderTerminalTaskDynamicMapper> {

    public ProviderTerminalTaskDAO(SqlSessionFactory sqlSessionFactory, ProviderTerminalTaskDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    public List<ProviderTerminalTaskDO> listByMerchantSnAndType(String merchantSn, Integer type) {
        LambdaQueryWrapper<ProviderTerminalTaskDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(ProviderTerminalTaskDO::getMerchantSn, merchantSn)
                .eq(ProviderTerminalTaskDO::getType, type);
        return entityMapper.selectList(lambdaQueryWrapper);
    }
}
