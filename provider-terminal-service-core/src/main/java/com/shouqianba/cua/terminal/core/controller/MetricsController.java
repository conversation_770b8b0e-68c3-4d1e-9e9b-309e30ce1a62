package com.shouqianba.cua.terminal.core.controller;

import com.shouqianba.cua.terminal.core.aop.annotation.ProviderTerminalEntry;
import com.wosai.middleware.hera.toolkit.metrics.MetricsManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 指标控制器
 *
 * <AUTHOR>
 * @date 2024/1/3 14:32
 */
@RestController
public class MetricsController {

    @GetMapping(path = "/metrics", produces = {"text/plain;version=0.0.4;charset=utf-8"})
    @ProviderTerminalEntry(skipLogging = true)
    public String endpoint() {
        return MetricsManager.scrape();
    }
}
