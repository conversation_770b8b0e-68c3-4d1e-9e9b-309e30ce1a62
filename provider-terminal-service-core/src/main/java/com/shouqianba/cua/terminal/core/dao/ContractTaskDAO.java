package com.shouqianba.cua.terminal.core.dao;

import com.shouqianba.cua.terminal.core.mapper.ContractTaskDynamicMapper;
import com.shouqianba.cua.terminal.core.model.entity.ContractSubTaskDO;
import com.shouqianba.cua.terminal.core.model.entity.ContractTaskDO;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;


/**
 * 总任务表表数据库访问层 {@link ContractTaskDO}
 * 对ContractTaskMapper层做出简单封装 {@link ContractTaskDynamicMapper}
 */
@Repository
public class ContractTaskDAO extends AbstractBaseDAO<ContractTaskDO, ContractTaskDynamicMapper> {

    @Resource
    private ContractSubTaskDAO contractSubTaskDAO;

    public ContractTaskDAO(SqlSessionFactory sqlSessionFactory, ContractTaskDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 批量插入任务(主任务和对应子任务)
     *
     * @param contractTaskDO     主任务
     * @param contractSubTaskDOS 子任务列表
     */
    @Transactional(rollbackFor = Exception.class)
    public Long batchInsertTasks(ContractTaskDO contractTaskDO, List<ContractSubTaskDO> contractSubTaskDOS) {
        insertOne(contractTaskDO);
        contractSubTaskDOS.forEach(t -> t.setPTaskId(contractTaskDO.getId()));
        contractSubTaskDAO.batchInsert(contractSubTaskDOS);
        return contractTaskDO.getId();
    }
}
