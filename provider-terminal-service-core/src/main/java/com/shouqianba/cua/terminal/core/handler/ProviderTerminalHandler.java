package com.shouqianba.cua.terminal.core.handler;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.terminal.core.handler.model.ProviderTerminalAddContext;

/**
 * <AUTHOR>
 * @date 2025/7/28
 */
public interface ProviderTerminalHandler {


    /**
     * 增加终端
     * @param context
     * @return
     */
    void addProviderTerminal(ProviderTerminalAddContext context);

    /**
     * 通道号
     * @return 通道号
     */
    ProviderEnum getProvider();
}
