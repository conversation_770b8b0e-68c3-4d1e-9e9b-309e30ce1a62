package com.shouqianba.cua.terminal.core.aop.annotation;

import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class ProviderTerminalEntryParser {

    public boolean isCommonLogging(MethodSignature methodSignature) {
        ProviderTerminalEntry providerTerminalEntry = methodSignature.getMethod()
                .getDeclaredAnnotation(ProviderTerminalEntry.class);
        return Objects.isNull(providerTerminalEntry)
                || (!providerTerminalEntry.skipLogging()
                && !providerTerminalEntry.skipLoggingParams());
    }

    public boolean isLoggingMethod(MethodSignature methodSignature) {
        ProviderTerminalEntry providerTerminalEntry = methodSignature.getMethod()
                .getDeclaredAnnotation(ProviderTerminalEntry.class);
        return Objects.nonNull(providerTerminalEntry) && !providerTerminalEntry.skipLogging()
                && providerTerminalEntry.skipLoggingParams();
    }

    public boolean isSkipValidate(MethodSignature methodSignature) {
        ProviderTerminalEntry providerTerminalEntry = methodSignature.getMethod()
                .getDeclaredAnnotation(ProviderTerminalEntry.class);
        return Objects.nonNull(providerTerminalEntry) && providerTerminalEntry.skipValidate();
    }
}
