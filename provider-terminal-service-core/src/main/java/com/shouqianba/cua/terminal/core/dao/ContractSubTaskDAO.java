package com.shouqianba.cua.terminal.core.dao;

import com.shouqianba.cua.terminal.core.mapper.ContractSubTaskDynamicMapper;
import com.shouqianba.cua.terminal.core.model.entity.ContractSubTaskDO;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;


/**
 * 子任务表表数据库访问层 {@link ContractSubTaskDO}
 * 对ContractSubTaskMapper层做出简单封装 {@link ContractSubTaskDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class ContractSubTaskDAO extends AbstractBaseDAO<ContractSubTaskDO, ContractSubTaskDynamicMapper> {

    public ContractSubTaskDAO(SqlSessionFactory sqlSessionFactory, ContractSubTaskDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }
}
