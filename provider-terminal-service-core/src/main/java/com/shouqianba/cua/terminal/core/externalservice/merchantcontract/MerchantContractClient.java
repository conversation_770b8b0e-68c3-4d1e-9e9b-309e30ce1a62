package com.shouqianba.cua.terminal.core.externalservice.merchantcontract;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.terminal.core.dao.McChannelDAO;
import com.shouqianba.cua.terminal.core.dao.MerchantProviderParamsDAO;
import com.shouqianba.cua.terminal.core.model.ContractChannel;
import com.shouqianba.cua.terminal.core.model.entity.McChannelDO;
import com.shouqianba.cua.terminal.core.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.ChannelParam;
import com.wosai.upay.merchant.contract.model.provider.TongLianV2Param;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 商户合约服务客户端
 * 对TonglianV2Service的防腐处理
 */
@Slf4j
@Component
public class MerchantContractClient {

    @Autowired
    private TongLianV2Service tonglianV2Service;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Autowired
    private McChannelDAO mcChannelDAO;

    /**
     * 添加通联终端
     *
     * @param merchantSn 商户号
     * @param acquirerMerchantId 收单商户号
     * @param providerTerminalId 终端ID
     * @param payMerchantId 支付商户ID
     * @return 是否添加成功
     */
    public boolean addTongLianTerminal(String merchantSn, String acquirerMerchantId, String providerTerminalId, String payMerchantId) {
        try {
            // 构建终端信息DTO
            AddTermInfoDTO addTermInfoDTO = new AddTermInfoDTO();
            addTermInfoDTO.setDeviceId(providerTerminalId);
            addTermInfoDTO.setSubMchId(acquirerMerchantId);
            addTermInfoDTO.setMerchantSn(merchantSn);

            log.info("[MerchantContractClient] 调用TonglianV2Service.addTerm开始, 请求参数: {}", JSON.toJSONString(addTermInfoDTO));
            // 构建通联参数
            TongLianV2Param tongLianV2Param = buildContractParamsByPayMchId(payMerchantId, merchantSn, TongLianV2Param.class);
            // 调用通联服务
            ContractResponse response = tonglianV2Service.addTerm(addTermInfoDTO, tongLianV2Param);
            
            log.info("[MerchantContractClient] 调用TonglianV2Service.addTerm成功, 返回结果: {}", JSON.toJSONString(response));
            return response.isSuccess();
        } catch (Exception e) {
            log.error("[MerchantContractClient] 调用TonglianV2Service.addTerm异常, 异常信息: ", e);
            throw e;
        }
    }

    private  <T> T buildContractParamsByPayMchId(String payMerchantId, String merchantSn, Class<T> tClass) {
        Optional<MerchantProviderParamsDO> paramsOptional = merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantIdAndMerchantSn(payMerchantId, merchantSn);
        if (paramsOptional.isEmpty()) {
            throw new CommonPubBizException("构建 " + tClass.getName() + " 失败");
        }
        MerchantProviderParamsDO merchantProviderParamsDO = paramsOptional.get();
        return buildContractParams(String.valueOf(merchantProviderParamsDO.getProvider()), merchantProviderParamsDO.getPayway(), merchantProviderParamsDO.getChannelNo(), tClass);
    }

    private <T> T buildContractParams(String provider, int payway, String channelNo, Class<T> tClass) {
        ContractChannel contractChannel = getContractChannel(payway, provider, channelNo);
        return buildParam(contractChannel, tClass);
    }

    private ContractChannel getContractChannel(int payway, String provider, String channelNo) {
        Optional<McChannelDO> mcChannel = mcChannelDAO.getMcChannel(payway, provider, channelNo);
        if (mcChannel.isEmpty()) {
            throw new CommonPubBizException("渠道 " + channelNo + "不存在");
        }
        return new ContractChannel(mcChannel.get());
    }

    public <T> T buildParam(ContractChannel contractChannel, Class<T> tClass) {
        String metadata = com.alibaba.fastjson.JSON.toJSONString(contractChannel.getChannelParam());
        T target = JSON.parseObject(metadata, tClass);
        if (target == null) {
            throw new CommonPubBizException("构建 " + tClass.getName() + " 失败");
        }
        BeanUtils.copyProperties(contractChannel, target);
        if (target instanceof ChannelParam) {
            ((ChannelParam) target).setPayway(contractChannel.getPayway());
            ((ChannelParam) target).setProvider(contractChannel.getProvider());
        }
        return target;
    }
}