package com.shouqianba.cua.terminal.core.handler;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.terminal.core.handler.model.ProviderTerminalAddContext;
import com.shouqianba.cua.terminal.core.model.enums.ProviderTerminalIdTypeEnum;
import com.shouqianba.cua.terminal.core.utils.ScaleConverterUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
@Component
public class UmbProviderTerminalHandler extends AbstractProviderTerminalHandler {


    @Override
    protected List<Integer> getSupportPaywayList() {
        return ALIPAY_WEIXIN_LIST;
    }

    @Override
    public ProviderEnum getProvider() {
        return ProviderEnum.PROVIDER_UMB;
    }

    @Override
    protected String contractProviderTerminalId(ProviderTerminalAddContext request) {
        ProviderTerminalIdTypeEnum type = isProdEnvironment ?
                ProviderTerminalIdTypeEnum.GENERAL_PREFIX : ProviderTerminalIdTypeEnum.GENERAL_TEST_PREFIX;
        String prefix = type.getCode();
        String nextTerminalId = ScaleConverterUtil.encode36(Long.parseLong(corebClient.nextProviderTerminalId()));
        if (StringUtils.isNotBlank(prefix)) {
            return prefix + nextTerminalId;
        }
        return nextTerminalId;
    }
}
