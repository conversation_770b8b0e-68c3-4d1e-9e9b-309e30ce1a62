package com.shouqianba.cua.terminal.core.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/8/7
 */
public enum ProviderTerminalTypeEnum implements ITextValueEnum<Integer> {

    OFFLINE(0, "线下终端"),
    ONLINE(1, "线上终端");

    private final String text;
    @Getter
    private final Integer value;

    ProviderTerminalTypeEnum(Integer value, String text) {
        this.text = text;
        this.value = value;
    }

    @Override
    public Integer getValue() {
        return 0;
    }

    @Override
    public String getText() {
        return "";
    }
}
