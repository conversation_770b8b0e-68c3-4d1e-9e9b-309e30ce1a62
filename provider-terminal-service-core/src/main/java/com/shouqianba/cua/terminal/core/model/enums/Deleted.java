package com.shouqianba.cua.terminal.core.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 删除枚举
 *
 * <AUTHOR>
 */
public enum Deleted implements ITextValueEnum<Integer> {
    /**
     * 未删除
     */
    NO_DELETED(0, "未删除"),
    /**
     * 已删除
     */
    DELETED(1, "已删除");

    private final Integer value;
    private final String text;

    Deleted(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
