package com.shouqianba.cua.terminal.core.biz;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.terminal.core.config.apollo.ApplicationApolloConfig;
import com.shouqianba.cua.terminal.core.dao.ProviderTerminalDAO;
import com.shouqianba.cua.terminal.core.dao.ProviderTerminalTaskDAO;
import com.shouqianba.cua.terminal.core.externalservice.coreb.CorebClient;
import com.shouqianba.cua.terminal.core.model.bo.ProviderTerminalContextBO;
import com.shouqianba.cua.terminal.core.model.entity.MerchantProviderParamsDO;
import com.shouqianba.cua.terminal.core.model.entity.ProviderTerminalDO;
import com.shouqianba.cua.terminal.core.model.entity.ProviderTerminalTaskDO;
import com.shouqianba.cua.terminal.core.model.enums.ProviderTerminalTaskStatusEnum;
import com.shouqianba.cua.terminal.core.model.enums.ProviderTerminalTaskTypeEnum;
import com.shouqianba.cua.terminal.core.model.enums.ProviderTerminalTypeEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Component
@Slf4j
public class ProviderTerminalBiz {
    @Autowired
    private ProviderTerminalDAO providerTerminalDAO;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    private ProviderTerminalTaskDAO providerTerminalTaskDAO;
    @Autowired
    private CorebClient corebClient;

    /**
     * 商户纬度 关联收单机构终端ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void createMerchantLevelProviderTerminal(String merchantSn, String providerTerminalId, String acquirerMerchantId, int provider) {
        //新增 provider_terminal表
        ProviderTerminalDO terminal = new ProviderTerminalDO();
        terminal.setMerchantSn(merchantSn);
        terminal.setProvider(provider);
        terminal.setProviderTerminalId(providerTerminalId);
        terminal.setAcquirerMerchantId(acquirerMerchantId);
        providerTerminalDAO.insertOne(terminal);

        // 处理交易配置
        corebClient.handleMerchantLevelTradeConfig(merchantSn, providerTerminalId, provider);
    }

    /**
     * 终端纬度 关联收单机构终端ID
     *
     * @param merchantSn         商户号
     * @param providerTerminalId 收单机构终端号
     * @param provider           收单机构对应的收钱吧提供者
     * @param terminalAppid      收钱吧终端
     * @param terminalSn
     * @param storeSn
     */
    public void createTerminalLevelProviderTerminal(String merchantSn, String providerTerminalId, String acquirerMerchantId, int provider
            , String terminalAppid, String terminalSn, String storeSn) {
        ProviderTerminalDO terminal = new ProviderTerminalDO();
        terminal.setMerchantSn(merchantSn);
        terminal.setProvider(provider);
        terminal.setProviderTerminalId(providerTerminalId);
        terminal.setAcquirerMerchantId(acquirerMerchantId);
        terminal.setStoreSn(storeSn);
        terminal.setTerminalSn(terminalSn);
        terminal.setTerminalAppid(terminalAppid);
        // 区分这是一个线上终端还是线下终端
        boolean isOnlineTerminal = applicationApolloConfig.getOnlineVendorAppids().contains(terminalAppid);
        if (isOnlineTerminal) {
            terminal.setType(ProviderTerminalTypeEnum.ONLINE.getValue());
        } else {
            terminal.setType(ProviderTerminalTypeEnum.OFFLINE.getValue());
        }
        providerTerminalDAO.insertOne(terminal);
    }


    /**
     * 门店纬度 关联收单机构终端ID
     *
     * @param merchantSn         商户号
     * @param providerTerminalId 收单机构终端号
     * @param acquirerMerchantId 收单机构商户号
     * @param provider           通道
     * @param storeSn            门店号
     */
    public void createStoreLevelProviderTerminal(String merchantSn, String providerTerminalId, String acquirerMerchantId, int provider, String storeSn) {
        ProviderTerminalDO terminal = new ProviderTerminalDO();
        terminal.setMerchantSn(merchantSn);
        terminal.setProvider(provider);
        terminal.setProviderTerminalId(providerTerminalId);
        terminal.setAcquirerMerchantId(acquirerMerchantId);
        terminal.setStoreSn(storeSn);
        providerTerminalDAO.insertOne(terminal);
    }


    /**
     * 收钱吧终端与当前子商户是否已经绑定,已绑定返回空集合,未绑定则返回需要绑定的交易参数的集合
     *
     * @param providerTerminal 当前终端记录
     * @param params           商户在某个通道下的支付宝和微信交易参数
     * @return
     */
    public List<MerchantProviderParamsDO> getNotSyncMerchantProviderParams(ProviderTerminalDO providerTerminal, List<MerchantProviderParamsDO> params) {
        // 组装查询条件
        //子商户集合
        final Set<String> subMerchantNoSet = params.parallelStream().map(MerchantProviderParamsDO::getPayMerchantId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        //是否已经报过
        final String boundSubMchIds = providerTerminal.getBoundSubMchIds();
        //子商户号没有绑定成功
        if (WosaiStringUtils.isEmpty(boundSubMchIds)) {
            return params;
        }
        final List<String> list = Splitter.on(",").omitEmptyStrings().splitToList(boundSubMchIds);
        final Sets.SetView<String> difference = Sets.difference(subMerchantNoSet, Sets.newHashSet(list));
        final ImmutableSet<String> diff = difference.immutableCopy();
        if (CollectionUtils.isEmpty(diff)) {
            return null;
        }
        //payMerchantId为key
        final Map<String, MerchantProviderParamsDO> paramsMap = params.parallelStream().collect(Collectors.toMap(MerchantProviderParamsDO::getPayMerchantId, param -> param, (val1, val2) -> val1));
        return diff.stream().map(payMerchantId ->
                paramsMap.get(String.valueOf(payMerchantId))
        ).filter(Objects::nonNull).collect(Collectors.toList());
    }


    /**
     * 设置缓存,写入3后自动删除
     */
    Cache<String, Boolean> existCache = CacheBuilder.newBuilder()
            .maximumSize(4000)
            .expireAfterWrite(3, TimeUnit.SECONDS)
            .build();

    /**
     * 新增子商户绑定所有终端
     *
     * @param merchantSn  商户号
     * @param subMerchant 子商户号
     * @param provider
     * @param payWay      支付方式
     * @param type        1-"新增子商户号绑定所有终端",2-"新增终端绑定所有子商户号",3-解绑当前终端对应的所有子商户号,4-"新增门店级别终端绑定所有子商户号"
     * @param terminalSn  收钱吧对应的终端号
     */
    public void addBoundTerminalTask(String merchantSn, String subMerchant, int provider, int payWay, Integer type, String providerTerminalId, String storeSn, String terminalSn, String brandMerchantSn) {
        // 检查是否已经绑定
        if (!WosaiStringUtils.isEmptyAny(subMerchant, providerTerminalId) && !Objects.equals(type, ProviderTerminalTaskTypeEnum.UNBIND_TERMINAL_ALL_SUB_MCH.getType())) {
            List<ProviderTerminalDO> providerTerminals = Optional.ofNullable(providerTerminalDAO.listByProviderTerminalId(providerTerminalId))
                    .orElseGet(ArrayList::new);
            //商户级别
            if (ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType() == type) {
                providerTerminals = providerTerminals.stream().filter(ter -> StringUtils.isAllBlank(ter.getStoreSn(), ter.getTerminalSn())).collect(Collectors.toList());
            }
            if (ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH.getType() == type) {
                providerTerminals = providerTerminals.stream().filter(ter -> StringUtils.isNoneBlank(ter.getStoreSn(), ter.getTerminalSn())).collect(Collectors.toList());
            }
            if (ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType() == type) {
                providerTerminals = providerTerminals.stream().filter(ter -> StringUtils.isNotBlank(ter.getStoreSn())).collect(Collectors.toList());
            }
            if (WosaiCollectionUtils.isNotEmpty(providerTerminals)) {
                boolean anyMatch = providerTerminals.stream().anyMatch(providerTerminal ->
                        WosaiStringUtils.isNotEmpty(providerTerminal.getBoundSubMchIds())
                                && providerTerminal.getBoundSubMchIds().contains(subMerchant)
                                && Objects.equals(providerTerminalId, providerTerminal.getProviderTerminalId())
                );
                if (anyMatch) {
                    return;
                }
            }
        }

        ProviderTerminalContextBO context = ProviderTerminalContextBO.builder()
                .subMerchant(subMerchant)
                .provider(provider)
                .payWay(payWay)
                .providerTerminalId(providerTerminalId)
                .sqbStoreSn(storeSn)
                .terminalSn(terminalSn)
                .brandMerchantSn(brandMerchantSn)
                .build();
        //是否存在相同的任务,避免重复创建
        String newContext = JSONObject.toJSONString(context);
        final List<ProviderTerminalTaskDO> taskList = providerTerminalTaskDAO.listByMerchantSnAndType(merchantSn, type);
        final boolean match = taskList.stream()
                .filter(task -> !Objects.equals(task.getStatus(), ProviderTerminalTaskStatusEnum.FAIL.getValue()))
                .anyMatch(task -> Objects.equals(newContext, task.getContext()));
        if (match) {
            return;
        }
        //不存在则插入
        ProviderTerminalTaskDO task = new ProviderTerminalTaskDO();
        task.setMerchantSn(merchantSn);
        task.setType(type);
        task.setContext(JSONObject.toJSONString(context));
//        华夏对于同一个终端需要间隔一段时间执行所以出现一下逻辑
        if (Objects.equals(provider, ProviderEnum.PROVIDER_HXB.getValue()) && WosaiStringUtils.isNotEmpty(providerTerminalId)) {
            Boolean present =
                    existCache.getIfPresent(providerTerminalId);
            if (Objects.isNull(present) || Objects.equals(present, Boolean.FALSE)) {
                //设置已经存在
                existCache.put(providerTerminalId, Boolean.TRUE);
            } else {
                Random random = new Random();
                // 恢复之前的getDelayTerminalTask()方法调用
                int delaySeconds = random.nextInt(30) + applicationApolloConfig.getDelayTerminalTask(); // 保留默认值0，因为getDelayTerminalTask()方法不存在
                //设置任务推迟时间
                task.setPriority(new Timestamp(DateUtils.addSeconds(new Date(), delaySeconds).getTime()));
            }
        }
        providerTerminalTaskDAO.insertOne(task);
    }

}
