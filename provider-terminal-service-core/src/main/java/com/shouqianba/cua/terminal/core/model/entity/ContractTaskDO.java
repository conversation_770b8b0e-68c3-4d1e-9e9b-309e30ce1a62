package com.shouqianba.cua.terminal.core.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shouqianba.cua.enums.contract.ContractTaskProcessStatusEnum;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Objects;

/**
 * 总任务表表实体对象
 *
 * <AUTHOR>
 */
@TableName("contract_task")
@Data
public class ContractTaskDO {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商户sn
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 商户名称
     */
    @TableField(value = "merchant_name")
    private String merchantName;
    /**
     * 新增商户入网,基本信息变更,结算账户变更,附件上传,费率变更
     */
    @TableField(value = "type")
    private String type;
    /**
     * 处理这个事件的消息内容 可以为空
     */
    @TableField(value = "event_msg")
    private String eventMsg;
    /**
     * 处理这个事件产生的商户上下文参数
     */
    @TableField(value = "event_context")
    private String eventContext;
    /**
     * 0待处理 1.处理中 3代付验证中  4被终止 5处理成功  6处理失败
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 影响主任务成功的子任务总数量
     */
    @TableField(value = "affect_sub_task_count")
    private Integer affectSubTaskCount;
    /**
     * 已经成功的 影响主任务成功的子任务数量
     */
    @TableField(value = "affect_status_success_task_count")
    private Integer affectStatusSuccessTaskCount;
    /**
     * {"channel":"","message":""} 成功时 目前不需要存 失败时需要存失败的通道信息和原因
     */
    @TableField(value = "result")
    private String result;

    @TableField(value = "create_at")
    private Timestamp createAt;

    @TableField(value = "update_at")
    private Timestamp updateAt;
    /**
     * 版本号 每次数据有更新+1
     */
    @TableField(value = "version")
    private Long version;

    @TableField(value = "priority")
    private Timestamp priority;

    @TableField(value = "complete_at")
    private Timestamp completeAt;
    /**
     * 报备规则组
     */
    @TableField(value = "rule_group_id")
    private String ruleGroupId;

    public boolean processSuccess() {
        return Objects.equals(this.status, ContractTaskProcessStatusEnum.PROCESS_SUCCESS.getValue());
    }

    public boolean processFail() {
        return Objects.equals(this.status, ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue());
    }

}

