package com.shouqianba.cua.terminal.core.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2022/3/9 10:31 上午
 */
@Getter
@AllArgsConstructor
public enum ProviderTerminalIdTypeEnum {


    MERCHANT_PREFIX("SM", "生产环境商户纬度终端ID前缀"),
    STORE_PREFIX("SS", "生产环境门店纬度终端ID前缀"),
    TERMINAL_PREFIX("ST", "生产环境终端纬度终端ID前缀"),
    MERCHANT_TEST_PREFIX("TM", "测试环境商户纬度终端ID前缀"),
    STORE_TEST_PREFIX("TS", "测试环境门店纬度终端ID前缀"),
    TERMINAL_TEST_PREFIX("TT", "测试环境终端纬度终端ID前缀"),

    GENERAL_PREFIX("SH", "生产环境终端ID前缀"),
    GENERAL_TEST_PREFIX("TH", "测试环境终端ID前缀"),
    ;

    private String code;
    private String message;
}
