package com.shouqianba.cua.terminal.core.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * <AUTHOR>
 * @date 2025/8/7
 */
public enum ProviderTerminalTaskStatusEnum implements ITextValueEnum<Integer> {

    PROCESSING(1, "处理中"),
    SUCCESS(2, "处理成功"),
    FAIL(3, "处理失败")
    ;

    private final Integer value;
    private final String text;
    ProviderTerminalTaskStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }


    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }
}
