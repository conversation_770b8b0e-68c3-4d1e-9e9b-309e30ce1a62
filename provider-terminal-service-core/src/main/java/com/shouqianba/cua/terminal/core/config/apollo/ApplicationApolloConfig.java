package com.shouqianba.cua.terminal.core.config.apollo;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/7
 */
@Data
@Component
public class ApplicationApolloConfig {

    @ApolloJsonValue("${online_vendor_appids:[]}")
    private List<String> onlineVendorAppids;

    @ApolloJsonValue("${delay_terminal_task:40}")
    private int delayTerminalTask;
}
