package com.shouqianba.cua.terminal.core.config.rpc;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.googlecode.jsonrpc4j.ErrorData;
import com.googlecode.jsonrpc4j.ErrorResolver;
import com.wosai.common.exception.CommonDataAccessException;
import com.wosai.common.exception.CommonDatabaseDuplicateKeyException;
import com.wosai.common.exception.CommonException;
import com.wosai.common.exception.CommonIOException;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonNetConnectErrorException;
import com.wosai.common.exception.CommonNullPointerException;
import com.wosai.common.exception.CommonUnknownException;
import com.wosai.common.exception.CommonUnknownHostException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.CannotGetJdbcConnectionException;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.io.IOException;
import java.lang.reflect.Method;
import java.net.ConnectException;
import java.net.UnknownHostException;
import java.util.List;
import java.util.StringJoiner;

/**
 * 统一异常处理
 *
 * <AUTHOR>
 * @date 2020-03-11
 */
@Component
@Slf4j
@SuppressWarnings("java:S6201")
public class JsonRpcErrorResolver implements ErrorResolver {
    @Override
    public JsonError resolveError(Throwable throwable, Method method, List<JsonNode> list) {
        String throwableName = throwable.getClass().getName();
        ErrorData defaultErrorData = new ErrorData(throwableName, throwable.getMessage());
        CommonException commonException = null;
        try {
            if (throwable instanceof ConstraintViolationException e) {
                StringJoiner message = new StringJoiner(";");
                for (ConstraintViolation<?> constraintViolation : e.getConstraintViolations()) {
                    message.add(constraintViolation.getMessage());
                }
                return new JsonError(CommonException.CODE_INVALID_PARAMETER, message.toString(), new ErrorData(CommonInvalidParameterException.class.getName(), message.toString()));
            } else if (throwable instanceof InvalidFormatException) {
                return new JsonError(CommonException.CODE_INVALID_PARAMETER, CommonException.getCodeDesc(CommonException.CODE_INVALID_PARAMETER), defaultErrorData);
            } else if (throwable instanceof CannotGetJdbcConnectionException) {
                commonException = new CommonDataAccessException(CommonException.getCodeDesc(CommonException.CODE_CANNOT_GET_JDBC_CONNECTION));
            } else if (throwable instanceof DuplicateKeyException) {
                commonException = new CommonDatabaseDuplicateKeyException(CommonException.getCodeDesc(CommonException.CODE_DATABASE_DUPLICATE_KEY));
            } else if (throwable instanceof DataAccessException) {
                commonException = new CommonDataAccessException(CommonException.getCodeDesc(CommonException.CODE_DATA_ACCESS_EXCEPTION));
            } else if (throwable instanceof UnknownHostException) {
                commonException = new CommonUnknownHostException(CommonException.getCodeDesc(CommonException.CODE_UNKNOWN_HOST_EXCEPTION));
            } else if (throwable instanceof ConnectException) {
                commonException = new CommonNetConnectErrorException(CommonException.getCodeDesc(CommonException.CODE_NET_CONNECT_ERROR));
            } else if (throwable instanceof IOException) {
                commonException = new CommonIOException(CommonException.getCodeDesc(CommonException.CODE_IO_EXCEPTION));
            } else if (throwable instanceof NullPointerException) {
                commonException = new CommonNullPointerException(CommonException.getCodeDesc(CommonException.CODE_NULL_POINTER_EXCEPTION));
            } else if (throwable instanceof CommonException ) {
                commonException = (CommonException) throwable;
            } else {
                commonException = new CommonUnknownException(CommonException.getCodeDesc(CommonException.CODE_UNKNOWN_ERROR));
            }
        } catch (Exception e) {
            log.error("jsonRpc error resolve exception", e);
        }
        if (commonException != null) {
            return new JsonError(commonException.getCode(), commonException.getMessage(), defaultErrorData);
        }
        return new JsonError(CommonException.CODE_UNKNOWN_ERROR, CommonException.getCodeDesc(CommonException.CODE_UNKNOWN_ERROR), defaultErrorData);
    }
}
