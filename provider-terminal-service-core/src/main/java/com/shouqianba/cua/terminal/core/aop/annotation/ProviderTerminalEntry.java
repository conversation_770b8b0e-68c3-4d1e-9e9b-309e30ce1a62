package com.shouqianba.cua.terminal.core.aop.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ProviderTerminalEntry {

    boolean skipValidate() default false;

    boolean skipLogging() default false;

    boolean skipLoggingParams() default false;
}
