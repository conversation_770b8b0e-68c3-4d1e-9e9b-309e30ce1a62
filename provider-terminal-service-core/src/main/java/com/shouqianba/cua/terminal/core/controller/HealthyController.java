package com.shouqianba.cua.terminal.core.controller;

import com.shouqianba.cua.terminal.core.aop.annotation.ProviderTerminalEntry;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 健康检查控制器
 *
 * <AUTHOR>
 * @date 2024/1/3 14:32
 */
@RestController
@RequestMapping("")
public class HealthyController {
    /**
     * 健康检查接口
     */
    @GetMapping( "/check")
    @ProviderTerminalEntry(skipLogging = true)
    public String healthy() {
        return "success";
    }

}
