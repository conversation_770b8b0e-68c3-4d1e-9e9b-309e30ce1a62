package com.shouqianba.cua.terminal.core.externalservice.coreb;

import com.alibaba.fastjson2.JSON;
import com.shouqianba.cua.terminal.core.externalservice.coreb.model.TerminalInfo;
import com.wosai.upay.core.bean.model.TradeExtConfigContentModel;
import com.wosai.upay.core.bean.request.TradeExtConfigCreateRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigQueryRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigUpdateRequest;
import com.wosai.upay.core.bean.response.TradeExtConfigQueryResponse;
import com.wosai.upay.core.service.SnGenerator;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class CorebClient {

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private SupportService supportService;

    @Autowired
    private TerminalService terminalService;

    @Autowired
    protected SnGenerator snGenerator;

    /**
     * 处理商户级别终端的交易配置
     *
     * @param merchantSn         商户号
     * @param providerTerminalId 终端ID
     * @param provider           供应商
     */
    public void handleMerchantLevelTradeConfig(String merchantSn, String providerTerminalId, int provider) {
        try {
            log.info("[CorebClient] 处理商户级别终端的交易配置, 商户号: {}, 终端ID: {}, 通道: {}", merchantSn, providerTerminalId, provider);
            //查询是否存在
            TradeExtConfigQueryResponse queryResponse = queryTradeExtConfig(merchantSn, TradeExtConfigQueryRequest.SN_TYPE_MERCHANT, provider);

            TradeExtConfigContentModel content = new TradeExtConfigContentModel();
            content.setTermId(providerTerminalId);

            if (Objects.isNull(queryResponse)) {
                //调用交易新增接口
                createTradeExtConfig(merchantSn, TradeExtConfigQueryRequest.SN_TYPE_MERCHANT, provider, providerTerminalId);
            } else {
                //调用交易修改接口
                updateTradeExtConfig(merchantSn, TradeExtConfigQueryRequest.SN_TYPE_MERCHANT, provider, providerTerminalId);
            }

            //清除交易缓存
            removeCachedParams(merchantSn);
        } catch (Exception e) {
            log.error("[CorebClient] 处理商户级别终端的交易配置异常, 异常信息: ", e);
            throw e;
        }
    }

    /**
     * 查询交易配置
     *
     * @return 查询结果
     */
    public TradeExtConfigQueryResponse queryTradeExtConfig(String sn, int snType, int provider) {
        try {
            TradeExtConfigQueryRequest queryRequest = new TradeExtConfigQueryRequest();
            queryRequest.setSn(sn);
            queryRequest.setSnType(snType);
            queryRequest.setProvider(provider);
            log.info("[CorebClient] 调用TradeConfigService.queryTradeExtConfig开始, 请求参数: {}", queryRequest);
            TradeExtConfigQueryResponse response = tradeConfigService.queryTradeExtConfig(queryRequest);
            log.info("[CorebClient] 调用TradeConfigService.queryTradeExtConfig成功, 返回结果: {}", response);
            return response;
        } catch (Exception e) {
            log.error("[CorebClient] 调用TradeConfigService.queryTradeExtConfig异常, 异常信息: ", e);
            throw e;
        }
    }

    /**
     * 创建交易配置
     */
    public void createTradeExtConfig(String sn, int snType, int provider, String terminalId) {
        try {
            TradeExtConfigContentModel content = new TradeExtConfigContentModel();
            content.setTermId(terminalId);
            TradeExtConfigCreateRequest request = new TradeExtConfigCreateRequest();
            request.setSn(sn);
            request.setSnType(snType);
            request.setProvider(provider);
            request.setContent(content);
            log.info("[CorebClient] 调用TradeConfigService.createTradeExtConfig开始, 请求参数: {}", request);
            tradeConfigService.createTradeExtConfig(request);
            log.info("[CorebClient] 调用TradeConfigService.createTradeExtConfig成功");
        } catch (Exception e) {
            log.error("[CorebClient] 调用TradeConfigService.createTradeExtConfig异常, 异常信息: ", e);
            throw e;
        }
    }

    /**
     * 更新交易配置
     */
    public void updateTradeExtConfig(String sn, int snType, int provider, String terminalId) {
        try {
            TradeExtConfigUpdateRequest request = new TradeExtConfigUpdateRequest();
            request.setSn(sn);
            request.setSnType(snType);
            request.setProvider(provider);
            TradeExtConfigContentModel content = new TradeExtConfigContentModel();
            content.setTermId(terminalId);
            request.setContent(content);
            log.info("[CorebClient] 调用TradeConfigService.updateTradeExtConfig开始, 请求参数: {}", request);
            tradeConfigService.updateTradeExtConfig(request);
            log.info("[CorebClient] 调用TradeConfigService.updateTradeExtConfig成功");
        } catch (Exception e) {
            log.error("[CorebClient] 调用TradeConfigService.updateTradeExtConfig异常, 异常信息: ", e);
            throw e;
        }
    }

    /**
     * 清除交易缓存
     *
     * @param merchantSn 商户号
     */
    public void removeCachedParams(String merchantSn) {
        try {
            log.info("[CorebClient] 调用SupportService.removeCachedParams开始, 请求参数: {}", merchantSn);
            supportService.removeCachedParams(merchantSn);
            log.info("[CorebClient] 调用SupportService.removeCachedParams成功");
        } catch (Exception e) {
            log.error("[CorebClient] 调用SupportService.removeCachedParams异常, 异常信息: ", e);
            throw e;
        }
    }

    public TerminalInfo getTerminalByTerminalSn(String terminalSn) {
        try {
            log.info("[CorebClient] 调用TerminalService.getTerminalByTerminalSn开始, 请求参数: {}", terminalSn);
            Map terminalInfo = terminalService.getTerminalByTerminalSn(terminalSn);
            log.info("[CorebClient] 调用TerminalService.getTerminalByTerminalSn成功, 返回结果: {}", JSON.toJSONString(terminalInfo));
            return new TerminalInfo(terminalInfo);
        } catch (Exception e) {
            log.error("[CorebClient] 调用TerminalService.getTerminalByTerminalSn异常, 异常信息: ", e);
            throw e;
        }
    }

    public String nextProviderTerminalId() {
        try {
            log.info("[CorebClient] 调用SnGenerator.nextProviderTerminalId开始");
            String terminalId = snGenerator.nextProviderTerminalId();
            log.info("[CorebClient] 调用SnGenerator.nextProviderTerminalId成功, 返回结果: {}", terminalId);
            return terminalId;
        } catch (Exception e) {
            log.error("[CorebClient] 调用SnGenerator.nextProviderTerminalId异常, 异常信息: ", e);
            throw e;
        }
    }
}