package com.shouqianba.cua.terminal.core.handler;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.terminal.api.enums.ProviderTerminalBindLevel;
import com.shouqianba.cua.terminal.api.exception.ProviderTerminalBizException;
import com.shouqianba.cua.terminal.core.dao.ContractTaskDAO;
import com.shouqianba.cua.terminal.core.handler.model.ProviderTerminalAddContext;
import com.shouqianba.cua.terminal.core.model.entity.ContractSubTaskDO;
import com.shouqianba.cua.terminal.core.model.entity.ContractTaskDO;
import com.shouqianba.cua.terminal.core.model.enums.ContractTaskTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
@Component
public class LzbProviderTerminalHandler extends AbstractProviderTerminalHandler {

    @Autowired
    private ContractTaskDAO contractTaskDAO;

    @Override
    protected List<Integer> getSupportPaywayList() {
        return EMPTY_PAYWAY_LIST;
    }

    @Override
    public ProviderEnum getProvider() {
        return ProviderEnum.PROVIDER_LZB;
    }

    @Override
    protected String contractProviderTerminalId(ProviderTerminalAddContext request) {
        if (ProviderTerminalBindLevel.MERCHANT.equals(request.getBindLevel())) {
            String merchantSn = request.getMerchantSn();
            ContractTaskDO task = new ContractTaskDO();
            task.setMerchantSn(merchantSn);
            task.setRuleGroupId(AcquirerTypeEnum.LZB.getValue());
            task.setStatus(0);
            task.setRuleGroupId(AcquirerTypeEnum.LZB.getValue());
            task.setType(ContractTaskTypeEnum.ADDTERM.getValue());
            task.setAffectSubTaskCount(0);
            task.setAffectSubTaskCount(1);

            ContractSubTaskDO subTaskDO = new ContractSubTaskDO();
            subTaskDO.setTaskType(11);
            subTaskDO.setStatusInfluPTask(1);
            subTaskDO.setChannel(AcquirerTypeEnum.LZB.getValue());
            subTaskDO.setMerchantSn(merchantSn);
            subTaskDO.setContractRule(AcquirerTypeEnum.LZB.getValue());
            subTaskDO.setRuleGroupId(AcquirerTypeEnum.LZB.getValue());
            subTaskDO.setScheduleDepTaskId(0L);
            subTaskDO.setScheduleStatus(1);
            contractTaskDAO.batchInsertTasks(task, Collections.singletonList(subTaskDO));
            return null;
        } else {
            throw new ProviderTerminalBizException("不支持的绑定级别");
        }
    }
}
