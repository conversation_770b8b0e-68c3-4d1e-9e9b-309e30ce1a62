package com.shouqianba.cua.terminal.core.config.rpc;

import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.wosai.upay.core.service.SnGenerator;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;


/**
 * 内部接口配置
 *
 * <AUTHOR>
 * @date 2024/1/5 15:28
 */
@Configuration
public class InternalRpcServiceConfig {

    @Value("${internal.service.core-business}")
    private String coreBusinessUrl;

    @Value("${internal.service.merchant-contract}")
    private String merchantContractUrl;

    /**
     * RestTemplate配置
     */
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    @Bean
    public JsonProxyFactoryBean tradeConfigService() {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(coreBusinessUrl + "/rpc/tradeConfig");
        jsonProxyFactoryBean.setServiceInterface(TradeConfigService.class);
        jsonProxyFactoryBean.setServerName("core-business");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);
        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean supportService() {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(coreBusinessUrl + "/rpc/support");
        jsonProxyFactoryBean.setServiceInterface(SupportService.class);
        jsonProxyFactoryBean.setServerName("core-business");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);
        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean corebMerchantService() {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(coreBusinessUrl + "/rpc/merchant");
        jsonProxyFactoryBean.setServiceInterface(com.wosai.upay.core.service.MerchantService.class);
        jsonProxyFactoryBean.setServerName("core-business");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);
        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean coreBStoreService() {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(coreBusinessUrl + "/rpc/store");
        jsonProxyFactoryBean.setServiceInterface(StoreService.class);
        jsonProxyFactoryBean.setServerName("core-business");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);
        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean snGenerator() {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(coreBusinessUrl + "/rpc/sn");
        jsonProxyFactoryBean.setServiceInterface(SnGenerator.class);
        jsonProxyFactoryBean.setServerName("core-business");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);
        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean terminalService() {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(coreBusinessUrl + "/rpc/terminal");
        jsonProxyFactoryBean.setServiceInterface(TerminalService.class);
        jsonProxyFactoryBean.setServerName("core-business");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);
        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean tonglianV2Service() {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(merchantContractUrl + "/rpc/tonglian_v2");
        jsonProxyFactoryBean.setServiceInterface(TongLianV2Service.class);
        jsonProxyFactoryBean.setServerName("merchant-contract");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(15000);
        return jsonProxyFactoryBean;
    }


}
