package com.shouqianba.cua.terminal.core.model.bo;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/8/7
 */
@Data
@Builder
public class ProviderTerminalContextBO {

    /**
     * 子商户户号
     */
    private String subMerchant;
    /**
     * 子商户号 provider
     */
    private Integer provider;
    /**
     *
     */
    private Integer payWay;



    //新增终端绑定子商户号 所需字段
    /**
     * 收单机构 终端ID
     */
    private String providerTerminalId;


    /**
     * 收钱吧对应门店
     */
    private String sqbStoreSn;

    /**
     * 收钱吧终端号
     */
    private String terminalSn;

    /**
     * 品牌商户号，品牌商户号与subMerchant是同一商户，只有品牌终端绑定才有这个字段
     * 现在所有的都可以传这个字段，如果不传，则使用任务的 merchant_sn获取对应的参数
     * 在绑定时需要通过这个字段和 subMerchant找到对应的交易参数。
     */
    private String brandMerchantSn;
}
