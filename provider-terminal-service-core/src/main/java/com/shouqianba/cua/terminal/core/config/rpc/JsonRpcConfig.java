package com.shouqianba.cua.terminal.core.config.rpc;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.jsonrpc4j.InvocationListener;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImplExporter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * jsonRpc4j configuration
 */
@Configuration
public class JsonRpcConfig {


    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    @Bean
    public AutoJsonRpcServiceImplExporter autoJsonRpcServiceImplExporter(JsonRpcErrorResolver jsonRpcErrorResolver, InvocationListener invocationListener) {
        AutoJsonRpcServiceImplExporter autoJsonRpcServiceImplExporter = new AutoJsonRpcServiceImplExporter();
        autoJsonRpcServiceImplExporter.setObjectMapper(objectMapper());
        autoJsonRpcServiceImplExporter.setErrorResolver(jsonRpcErrorResolver);
        autoJsonRpcServiceImplExporter.setInvocationListener(invocationListener);
        return autoJsonRpcServiceImplExporter;
    }


}
