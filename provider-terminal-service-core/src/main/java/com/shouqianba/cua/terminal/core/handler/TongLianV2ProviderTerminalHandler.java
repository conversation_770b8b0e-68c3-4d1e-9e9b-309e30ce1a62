package com.shouqianba.cua.terminal.core.handler;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.terminal.api.enums.ProviderTerminalBindLevel;
import com.shouqianba.cua.terminal.core.externalservice.merchantcontract.MerchantContractClient;
import com.shouqianba.cua.terminal.core.handler.model.ProviderTerminalAddContext;
import com.shouqianba.cua.terminal.core.model.enums.ProviderTerminalIdTypeEnum;
import com.shouqianba.cua.terminal.core.utils.ScaleConverterUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
@Component
public class TongLianV2ProviderTerminalHandler extends AbstractProviderTerminalHandler {

    @Autowired
    private MerchantContractClient merchantContractClient;


    @Override
    protected List<Integer> getSupportPaywayList() {
        return ALIPAY_WEIXIN_UNIONPAY_LIST;
    }

    @Override
    public ProviderEnum getProvider() {
        return ProviderEnum.PROVIDER_TONGLIAN_V2;
    }

    @Override
    protected String contractProviderTerminalId(ProviderTerminalAddContext request) {
        ProviderTerminalIdTypeEnum type;
        if (request.getBindLevel() == ProviderTerminalBindLevel.MERCHANT) {
            type = isProdEnvironment ?
                    ProviderTerminalIdTypeEnum.MERCHANT_PREFIX :
                    ProviderTerminalIdTypeEnum.MERCHANT_TEST_PREFIX;
        } else if (request.getBindLevel() == ProviderTerminalBindLevel.STORE) {
            type = isProdEnvironment ?
                    ProviderTerminalIdTypeEnum.STORE_PREFIX :
                    ProviderTerminalIdTypeEnum.STORE_TEST_PREFIX;
        } else {
            type = isProdEnvironment ?
                    ProviderTerminalIdTypeEnum.TERMINAL_PREFIX :
                    ProviderTerminalIdTypeEnum.TERMINAL_TEST_PREFIX;
        }

        String prefix = type.getCode();
        String nextTerminalId = ScaleConverterUtil.encode36(Long.parseLong(corebClient.nextProviderTerminalId()));
        String providerTerminalId = StringUtils.isNotBlank(prefix) ? prefix + nextTerminalId : nextTerminalId;

        boolean success = merchantContractClient.addTongLianTerminal(
                request.getMerchantSn(),
                request.getAcquirerMerchantInfo().getAcquirerMerchantId(),
                providerTerminalId,
                request.getAcquirerMerchantInfo().getAcquirerMerchantId()
        );

        if (!success) {
            throw new ContractBizException("通联收银宝终端采集信息失败:" + request.getMerchantSn());
        }
        return providerTerminalId;
    }
}
