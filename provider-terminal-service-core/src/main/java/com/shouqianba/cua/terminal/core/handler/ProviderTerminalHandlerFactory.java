package com.shouqianba.cua.terminal.core.handler;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.terminal.api.exception.ProviderTerminalBizException;
import com.shouqianba.cua.terminal.core.handler.model.ProviderTerminalAddContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/28
 */
@Component
public class ProviderTerminalHandlerFactory {

    @Autowired
    private List<ProviderTerminalHandler> providerTerminalHandlerList;

    public void addProviderTerminal(ProviderTerminalAddContext context) {
        ProviderEnum provider = context.getAcquirerMerchantInfo().getProvider();
        for (ProviderTerminalHandler providerTerminalHandler : providerTerminalHandlerList) {
            if (providerTerminalHandler.getProvider().equals(provider)) {
                providerTerminalHandler.addProviderTerminal(context);
                return;
            }
        }
        throw new ProviderTerminalBizException("找不到对应的处理类");
    }

}
