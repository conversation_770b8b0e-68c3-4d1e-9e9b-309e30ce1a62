package com.shouqianba.cua.terminal.core.aop;

import com.shouqianba.cua.terminal.api.dto.common.ApiResponse;
import com.shouqianba.cua.terminal.api.enums.ProviderTerminalCodeEnum;
import com.shouqianba.cua.terminal.api.exception.ProviderTerminalBizException;
import com.shouqianba.cua.terminal.core.aop.annotation.ProviderTerminalEntryParser;
import com.shouqianba.cua.terminal.core.utils.ValidationUtils;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.marker.Markers;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Aspect
@Order(Integer.MIN_VALUE + 1)
public class ProviderTerminalMethodInterceptor {

    @Autowired
    private ProviderTerminalEntryParser providerTerminalEntryParser;

    private static final long NANO_TO_MILLIS = 1000 * 1000;

    @Pointcut("execution (* com.shouqianba.cua.terminal.core.service.*.*(..)))")
    public void rpcPoint() {
    }

    @Pointcut("execution (* com.shouqianba.cua.terminal.core.controller.*.*(..)))")
    public void restPoint() {
    }

    @Around("rpcPoint() || restPoint()")
    public Object invoke(ProceedingJoinPoint point) {
        long begin = System.nanoTime();
        MethodSignature methodSignature = (MethodSignature) point.getSignature();
        String calledMethod = methodSignature.getDeclaringType().getSimpleName()
                + "#" + methodSignature.getName();
        Object result;
        try {
            loggingRequest(methodSignature, point.getArgs(), calledMethod);
            validate(point);
            result = point.proceed();
            loggingResult(methodSignature, result, begin, calledMethod);
        } catch (ProviderTerminalBizException e) {
            doLog(calledMethod, null, null, e, (System.nanoTime() - begin) / NANO_TO_MILLIS, Level.WARN);
            return genExceptionApiResponse(methodSignature.getReturnType(), e.getCode(), e.getMessage());
        } catch (Throwable t) {
            doLog(calledMethod, null, null, t, (System.nanoTime() - begin) / NANO_TO_MILLIS, Level.ERROR);
            return genExceptionApiResponse(methodSignature.getReturnType(), ProviderTerminalCodeEnum.SERVER_ERROR.getValue(), ProviderTerminalCodeEnum.SERVER_ERROR.getText());
        }
        return result;
    }

    private Object genExceptionApiResponse(Class<?> resultClass, int code, String msg) {
        try {
            Object obj = resultClass.getDeclaredConstructor().newInstance();
            if (obj instanceof ApiResponse) {
                return ApiResponse.fail(code, msg);
            }
            return null;
        } catch (Throwable ignored) {
            return null;
        }
    }

    private void loggingRequest(MethodSignature methodSignature, Object[] requests
            , String calledMethod) {
        try {
            if (providerTerminalEntryParser.isCommonLogging(methodSignature)) {
                doLog(calledMethod, requests, null, null, null, Level.INFO);
                return;
            }
            if (providerTerminalEntryParser.isLoggingMethod(methodSignature)) {
                doLog(calledMethod, null, null, null, null, Level.INFO);
            }
        } catch (Throwable throwable) {
            try {
                log.error("[记录入参日志异常]>>>>>>异常栈: ", throwable);
            } catch (Throwable ignored) {
            }
        }
    }

    public void loggingResult(MethodSignature methodSignature, Object result
            , long begin, String calledMethod) {
        try {
            long duration = (System.nanoTime() - begin) / NANO_TO_MILLIS;
            if (providerTerminalEntryParser.isCommonLogging(methodSignature)) {
                doLog(calledMethod, null, result, null, duration, Level.INFO);
                return;
            }
            if (providerTerminalEntryParser.isLoggingMethod(methodSignature)) {
                doLog(calledMethod, null, null, null, duration, Level.INFO);
            }
        } catch (Throwable throwable) {
            try {
                log.error("[记录出参日志异常]>>>>>>异常栈: ", throwable);
            } catch (Throwable ignored) {
            }
        }
    }

    private void doLog(String methodName, Object[] request, Object result, Throwable t, Long duration, Level level) {
        Map<String, Object> toAppendEntriesMap = new HashMap<>(6);
        toAppendEntriesMap.put("method", methodName);
        if (Objects.nonNull(request)) {
            toAppendEntriesMap.put("request", request);
        }
        if (Objects.nonNull(result)) {
            toAppendEntriesMap.put("response", result);
        }
        if (Objects.nonNull(duration)) {
            toAppendEntriesMap.put("duration", duration);
        }
        if (Objects.nonNull(t)) {
            toAppendEntriesMap.put("error", StringUtils.isEmpty(t.getMessage()) ? t.getCause().getMessage() : t.getMessage());
        }
        switch (level) {
            case INFO -> log.info(Markers.appendEntries(toAppendEntriesMap), "");
            case WARN -> log.warn(Markers.appendEntries(toAppendEntriesMap), "");
            case ERROR -> log.error(Markers.appendEntries(toAppendEntriesMap), "");
            default -> throw new RuntimeException();
        }
    }

    private void validate(ProceedingJoinPoint point) {
        Object[] requests = point.getArgs();
        if (ArrayUtils.isEmpty(requests)) {
            return;
        }
        MethodSignature methodSignature = ((MethodSignature) point.getSignature());
        if (providerTerminalEntryParser.isSkipValidate(methodSignature)) {
            return;
        }
        for (Object request : requests) {
            if (Objects.nonNull(request)) {
                ValidationUtils.ValidationResult validationResult;
                validationResult = ValidationUtils.validate(request);
                if (validationResult.isInvalid()) {
                    throw new ProviderTerminalBizException(ProviderTerminalCodeEnum.ILLEGAL_ARGUMENT
                            , validationResult.getMsg());
                }
            }
        }
    }
}
