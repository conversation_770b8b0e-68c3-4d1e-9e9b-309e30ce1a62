package com.shouqianba.cua.terminal.core.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 收单机构终端表表实体对象
 *
 * <AUTHOR>
 */
@TableName("provider_terminal")
@Data
public class ProviderTerminalDO {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 门店号
     */
    @TableField(value = "store_sn")
    private String storeSn;
    /**
     * 终端号
     */
    @TableField(value = "terminal_sn")
    private String terminalSn;
    /**
     * 终端vendor_app_appid
     */
    @TableField(value = "terminal_appid")
    private String terminalAppid;
    /**
     * 收单机构编号
     */
    @TableField(value = "provider")
    private Integer provider;
    /**
     * 收单机构终端id
     */
    @TableField(value = "provider_terminal_id")
    private String providerTerminalId;

    /**
     * 收单机构商户号
     */
    @TableField(value = "acquirer_merchant_id")
    private String acquirerMerchantId;

    /**
     * 已绑定的支付源子商户号列表
     */
    @TableField(value = "bound_sub_mch_ids")
    private String boundSubMchIds;
    /**
     * 终端类型 0线下终端 1线上终端
     */
    @TableField(value = "type")
    private Integer type;

    @TableField(value = "create_at")
    private Timestamp createAt;

    @TableField(value = "update_at")
    private Timestamp updateAt;


}

