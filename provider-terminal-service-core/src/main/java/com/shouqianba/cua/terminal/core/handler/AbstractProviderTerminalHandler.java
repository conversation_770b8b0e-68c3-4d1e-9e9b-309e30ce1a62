package com.shouqianba.cua.terminal.core.handler;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.terminal.api.enums.ProviderTerminalBindLevel;
import com.shouqianba.cua.terminal.api.enums.ProviderTerminalCodeEnum;
import com.shouqianba.cua.terminal.api.exception.ProviderTerminalBizException;
import com.shouqianba.cua.terminal.core.biz.ProviderTerminalBiz;
import com.shouqianba.cua.terminal.core.dao.MerchantProviderParamsDAO;
import com.shouqianba.cua.terminal.core.dao.ProviderTerminalDAO;
import com.shouqianba.cua.terminal.core.externalservice.coreb.CorebClient;
import com.shouqianba.cua.terminal.core.externalservice.coreb.model.TerminalInfo;
import com.shouqianba.cua.terminal.core.handler.model.ProviderTerminalAcquirerMerchantInfo;
import com.shouqianba.cua.terminal.core.handler.model.ProviderTerminalAddContext;
import com.shouqianba.cua.terminal.core.model.entity.MerchantProviderParamsDO;
import com.shouqianba.cua.terminal.core.model.entity.ProviderTerminalDO;
import com.shouqianba.cua.terminal.core.model.enums.ProviderTerminalTaskTypeEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.core.service.SnGenerator;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 抽象终端处理器
 * 处理不同级别的终端绑定（商户级、门店级、终端级）
 *
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
public abstract class AbstractProviderTerminalHandler implements ProviderTerminalHandler {

    // 静态变量定义常用的支付方式列表
    protected static final List<Integer> ALIPAY_WEIXIN_LIST = Arrays.asList(
            PaywayEnum.ALIPAY.getValue(),
            PaywayEnum.WEIXIN.getValue()
    );
    
    protected static final List<Integer> ALIPAY_WEIXIN_UNIONPAY_LIST = Arrays.asList(
            PaywayEnum.ALIPAY.getValue(), 
            PaywayEnum.WEIXIN.getValue(), 
            PaywayEnum.UNIONPAY.getValue()
    );
    
    protected static final List<Integer> EMPTY_PAYWAY_LIST = Collections.emptyList();

    @Autowired
    private ProviderTerminalBiz providerTerminalBiz;
    @Autowired
    protected Environment environment;
    @Autowired
    protected MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Autowired
    private ProviderTerminalDAO providerTerminalDAO;
    @Autowired
    private Validator validator;
    @Autowired
    protected CorebClient corebClient;
    protected boolean isProdEnvironment;

    @Autowired
    public void setEnvironment(Environment environment) {
        this.environment = environment;
        // 在初始化时确定环境，避免每次调用都进行判断
        this.isProdEnvironment = Arrays.asList(environment.getActiveProfiles()).contains("prod");
    }

    @Override
    public void addProviderTerminal(ProviderTerminalAddContext context) {
        validator.validate(context);

        ProviderTerminalBindLevel bindLevel = context.getBindLevel();

        switch (bindLevel) {
            case MERCHANT:
                processMerchantLevelBind(context);
            case STORE:
                processStoreLevelBind(context);
            case TERMINAL:
                processTerminalLevelBind(context);
            default:
                throw new ProviderTerminalBizException("不支持的绑定级别");
        }
    }

    /**
     * 处理商户级绑定
     */
    private void processMerchantLevelBind(ProviderTerminalAddContext context) {
        ProviderTerminalAcquirerMerchantInfo acquirerMerchantInfo = context.getAcquirerMerchantInfo();

        // 检查是否已存在终端
        Optional<ProviderTerminalDO> existingTerminal = providerTerminalDAO.getMerchantLevelProviderTerminal(
                acquirerMerchantInfo.getProvider().getValue(),
                context.getMerchantSn(),
                acquirerMerchantInfo.getAcquirerMerchantId());

        // 获取或创建终端ID
        String providerTerminalId = getOrCreateProviderTerminalId(context, existingTerminal);
        if (WosaiStringUtils.isEmpty(providerTerminalId)) {
            return;
        }

        // 如果不存在终端且成功创建，则创建商户级别终端
        if (existingTerminal.isEmpty()) {
            providerTerminalBiz.createMerchantLevelProviderTerminal(
                    context.getMerchantSn(),
                    providerTerminalId,
                    acquirerMerchantInfo.getAcquirerMerchantId(),
                    acquirerMerchantInfo.getProvider().getValue());
        }

        // 创建绑定任务
        createBindTasks(context, acquirerMerchantInfo, existingTerminal, providerTerminalId, null, null);
    }

    /**
     * 处理门店级绑定
     */
    private void processStoreLevelBind(ProviderTerminalAddContext context) {
        ProviderTerminalAcquirerMerchantInfo acquirerMerchantInfo = context.getAcquirerMerchantInfo();
        if (!checkNeedAddProviderTerminal(context)) {
            return;
        }

        // 检查是否已存在终端
        Optional<ProviderTerminalDO> existingTerminal = providerTerminalDAO.getStoreLevelProviderTerminal(
                acquirerMerchantInfo.getProvider().getValue(),
                context.getMerchantSn(),
                context.getStoreSn(),
                acquirerMerchantInfo.getAcquirerMerchantId());

        // 获取或创建终端ID
        String providerTerminalId = getOrCreateProviderTerminalId(context, existingTerminal);
        if (WosaiStringUtils.isEmpty(providerTerminalId)) {
            return;
        }
        // 如果不存在终端且成功创建，则建立连接
        if (existingTerminal.isEmpty()) {
            providerTerminalBiz.createStoreLevelProviderTerminal(
                    context.getMerchantSn(),
                    providerTerminalId,
                    acquirerMerchantInfo.getAcquirerMerchantId(),
                    acquirerMerchantInfo.getProvider().getValue(),
                    context.getStoreSn());
        }

        // 创建绑定任务
        createBindTasks(context, acquirerMerchantInfo, existingTerminal, providerTerminalId, context.getStoreSn(), null);
    }

    /**
     * 处理终端级绑定
     */
    private void processTerminalLevelBind(ProviderTerminalAddContext context) {
        ProviderTerminalAcquirerMerchantInfo acquirerMerchantInfo = context.getAcquirerMerchantInfo();
        if (!checkNeedAddProviderTerminal(context)) {
            return;
        }
        // 获取终端信息
        TerminalInfo terminalInfo = corebClient.getTerminalByTerminalSn(context.getTerminalSn());
        if (Objects.isNull(terminalInfo)) {
            throw new ProviderTerminalBizException(ProviderTerminalCodeEnum.ILLEGAL_ARGUMENT, "终端号不存在");
        }
        String storeSn = terminalInfo.getStoreSn();
        String vendorAppAppid = terminalInfo.getVendorAppAppid();

        // 检查是否已存在终端
        Optional<ProviderTerminalDO> existingTerminal = providerTerminalDAO.getTerminalLevelProviderTerminal(
                acquirerMerchantInfo.getProvider().getValue(),
                context.getMerchantSn(),
                storeSn,
                context.getTerminalSn(),
                acquirerMerchantInfo.getAcquirerMerchantId());

        // 获取或创建终端ID
        String providerTerminalId = getOrCreateProviderTerminalId(context, existingTerminal);
        if (WosaiStringUtils.isEmpty(providerTerminalId)) {
            return;
        }
        // 如果不存在终端且成功创建，则建立连接
        if (existingTerminal.isEmpty()) {
            providerTerminalBiz.createTerminalLevelProviderTerminal(
                    context.getMerchantSn(),
                    providerTerminalId,
                    acquirerMerchantInfo.getAcquirerMerchantId(),
                    acquirerMerchantInfo.getProvider().getValue(),
                    vendorAppAppid,
                    context.getTerminalSn(),
                    storeSn);
        }

        // 创建绑定任务
        createBindTasks(context, acquirerMerchantInfo, existingTerminal, providerTerminalId, storeSn, context.getTerminalSn());
    }

    /**
     * 校验是否需要添加终端  如果该通道支持绑定子商户号&子商户号都是空的话，则不添加终端
     *
     * @param context 请求上下文
     * @return true：需要添加终端，false：不需要添加终端
     */
    private boolean checkNeedAddProviderTerminal(ProviderTerminalAddContext context) {
        if (WosaiCollectionUtils.isNotEmpty(getSupportPaywayList())) {
            List<MerchantProviderParamsDO> params = merchantProviderParamsDAO.getParamsBySnAndPayWayListAndProvider(context.getAcquirerMerchantInfo().getMerchantSn(), getSupportPaywayList(), getProvider().getValue());
            if (WosaiCollectionUtils.isEmpty(params)) {
                log.info("该通道支持绑定子商户号，但未找到需要绑定的子商户号，不生成终端和绑定任务，上下文：{}", JSON.toJSONString(context));
                return false;
            }
        }
        return true;
    }

    /**
     * 获取或创建终端ID
     */
    private String getOrCreateProviderTerminalId(ProviderTerminalAddContext context, Optional<ProviderTerminalDO> existingTerminal) {
        if (existingTerminal.isPresent()) {
            log.info("终端已经存在ID: {}, 上下文：{}", existingTerminal.get().getProviderTerminalId(), JSON.toJSONString(context));
            return existingTerminal.get().getProviderTerminalId();
        }
        if (WosaiStringUtils.isNotEmpty(context.getProviderTerminalId())) {
            return context.getProviderTerminalId();
        }
        String newTerminalId = contractProviderTerminalId(context);
        if (WosaiStringUtils.isNotEmpty(newTerminalId)) {
            log.info("创建新的终端ID: {}, 上下文: {}", newTerminalId, JSON.toJSONString(context));
        }
        return newTerminalId;
    }

    /**
     * 创建绑定任务
     */
    private void createBindTasks(ProviderTerminalAddContext context,
                                 ProviderTerminalAcquirerMerchantInfo acquirerMerchantInfo,
                                 Optional<ProviderTerminalDO> existingTerminal,
                                 String providerTerminalId,
                                 String storeSn,
                                 String terminalSn) {
        if (Objects.isNull(providerTerminalId)) {
            return;
        }

        List<MerchantProviderParamsDO> needBindParams = getNeedBindParams(acquirerMerchantInfo, existingTerminal);

        if (!needBindParams.isEmpty()) {
            ProviderTerminalTaskTypeEnum taskType = determineTaskType(context.getBindLevel());

            needBindParams.forEach(param ->
                    providerTerminalBiz.addBoundTerminalTask(
                            context.getMerchantSn(),
                            param.getPayMerchantId(),
                            param.getProvider(),
                            param.getPayway(),
                            taskType.getType(),
                            providerTerminalId,
                            storeSn,
                            terminalSn,
                            acquirerMerchantInfo.getMerchantSn()));

            log.info("创建绑定任务完成, 上下文: {}, 交易参数: {}", JSON.toJSONString(context), JSON.toJSONString(needBindParams));
        }
    }

    /**
     * 确定任务类型
     */
    private ProviderTerminalTaskTypeEnum determineTaskType(ProviderTerminalBindLevel bindLevel) {
        return switch (bindLevel) {
            case MERCHANT -> ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL;
            case STORE -> ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH;
            case TERMINAL -> ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH;
        };
    }

    /**
     * 获取需要绑定的参数列表
     */
    private List<MerchantProviderParamsDO> getNeedBindParams(ProviderTerminalAcquirerMerchantInfo acquirerMerchantInfo,
                                                             Optional<ProviderTerminalDO> existingTerminal) {
        // 获取收单机构参数
        Optional<MerchantProviderParamsDO> optionalMerchantProviderParamsDO = merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantIdAndMerchantSn(
                acquirerMerchantInfo.getAcquirerMerchantId(),
                acquirerMerchantInfo.getMerchantSn());

        if (optionalMerchantProviderParamsDO.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取支持的支付方式列表
        List<Integer> supportPaywayList = getSupportPaywayList();
        if (WosaiCollectionUtils.isEmpty(supportPaywayList)) {
            return new ArrayList<>();
        }

        // 查询需要绑定的参数
        List<MerchantProviderParamsDO> params = merchantProviderParamsDAO.getParamsBySnAndPayWayListAndProvider(acquirerMerchantInfo.getMerchantSn(), getSupportPaywayList(), getProvider().getValue());

        // 如果存在终端，过滤掉已同步的参数
        if (existingTerminal.isPresent()) {
            return providerTerminalBiz.getNotSyncMerchantProviderParams(existingTerminal.get(), params);
        } else {
            return params;
        }
    }

    /**
     * 获取支持的支付方式列表
     */
    protected abstract List<Integer> getSupportPaywayList();

    /**
     * 创建终端ID
     */
    abstract String contractProviderTerminalId(ProviderTerminalAddContext request);
}
