package com.shouqianba.cua.terminal.core.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 报备渠道表
 * @TableName mc_channel
 */
@TableName(value ="mc_channel")
@Data
@Accessors(chain = true)
public class McChannelDO implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 渠道唯一标识
     */
    @TableField(value = "channel")
    private String channel;

    /**
     * 渠道名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 渠道主体
     */
    @TableField(value = "subject")
    private String subject;

    /**
     * 支付源
     */
    @TableField(value = "payway")
    private Integer payway;

    /**
     * 支付源渠道号
     */
    @TableField(value = "payway_channel_no")
    private String paywayChannelNo;

    /**
     * 渠道号（可能是结算通道渠道号也可能是支付源渠道号）
     */
    @TableField(value = "channel_no")
    private String channelNo;

    /**
     * 渠道秘钥
     */
    @TableField(value = "private_key")
    private String privateKey;

    /**
     * 结算通道
     */
    @TableField(value = "provider")
    private String provider;

    /**
     * 结算通道参数
     */
    @TableField(value = "provider_metadata")
    private String providerMetadata;

    /**
     * 收单机构
     */
    @TableField(value = "acquirer")
    private String acquirer;

    /**
     * 收单机构参数
     */
    @TableField(value = "acquirer_metadata")
    private String acquirerMetadata;

    /**
     * 状态 0禁用  1启用
     */
    private Integer status;

    /**
     * 
     */
    @TableField(value = "create_at")
    private Date createAt;

    /**
     * 
     */
    @TableField(value = "update_at")
    private Date updateAt;
}