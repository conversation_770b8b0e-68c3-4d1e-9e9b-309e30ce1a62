<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.shouqianba.cua</groupId>
        <artifactId>provider-terminal-service</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>provider-terminal-service-core</artifactId>
    <packaging>jar</packaging>

    <name>provider-terminal-service-core</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.shouqianba.cua</groupId>
            <artifactId>provider-terminal-service-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>cua-common</artifactId>
                    <groupId>com.shouqianba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shouqianba</groupId>
            <artifactId>cua-common</artifactId>
            <version>0.3.29</version>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.3.4</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>jsonrpc4j</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>apollo-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>snakeyaml</artifactId>
                    <groupId>org.yaml</groupId>
                </exclusion>
            </exclusions>
            <version>2.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>


        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>wosai-database-instrumentation-springboot2</artifactId>
            <version>5.0.2</version>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>6.4</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>aliyun-sdk-log</artifactId>
            <version>1.0.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>protobuf-java</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>logging-api</artifactId>
            <version>1.2.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-trace</artifactId>
            <version>1.7.2</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-logback-1.x</artifactId>
            <version>1.7.2</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-metrics</artifactId>
            <version>1.7.2</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.common</groupId>
            <artifactId>wosai-common</artifactId>
            <version>1.6.7</version>
        </dependency>

        <!--jsonRpc需要的依赖-->
        <dependency>
            <groupId>com.sun.xml.ws</groupId>
            <artifactId>jaxws-ri</artifactId>
            <version>4.0.2</version>
            <type>pom</type>
        </dependency>


        <!--internal service ======= -->
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>core-business-api</artifactId>
            <version>3.7.40</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>merchant-contract-api</artifactId>
            <version>2.66.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>merchant-contract-job-api</artifactId>
                    <groupId>com.wosai.upay</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.nextgen</groupId>
            <artifactId>data-common</artifactId>
            <version>2.0-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-databus-api</artifactId>
            <version>1.0.2</version>
        </dependency>


        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.47</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>vault-sdk</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>1.4.197</version>
            <scope>test</scope>
        </dependency>


    </dependencies>

    <build>
        <finalName>provider-terminal-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!--maven install|deploy时，跳过本模块-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>wosai-logging-maven-plugin</artifactId>
                <version>1.8.0</version>
                <configuration>
                    <enableCallerData>true</enableCallerData>
                    <profiles>
                        <profile>
                            <name>vpc,prod</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_JSON</ref> <!-- 以JSON格式输出到控制台，适用于线上ACK -->
                            </references>
                        </profile>
                        <profile>
                            <name>beta</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_JSON</ref> <!-- 以JSON格式输出到文件，适用于华北三弹性环境 -->
                            </references>
                        </profile>
                        <profile>
                            <name>default,dev</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_PATTERN</ref> <!-- 以字符串格式化形式输出到控制台，即标准输出，适用于本地开发 -->
                            </references>
                        </profile>
                    </profiles>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate-logback-spring</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>


</project>
