# Java编码规范

## 包命名规范
- 使用小写字母，用点分隔
- 包名应该反映模块功能：`com.shouqianba.cua.terminal.{module}`

## 类命名规范
- 使用PascalCase（大驼峰命名）
- 实体类以`DO`结尾：`ProviderTerminalDO`
- DTO类以`DTO`结尾：`ProviderTerminalAcquirerMerchantInfoDTO`
- 枚举类以`Enum`结尾：`ProviderTerminalCodeEnum`
- 异常类以`Exception`结尾：`ProviderTerminalBizException`

## 方法命名规范
- 使用camelCase（小驼峰命名）
- 动词开头，描述方法功能
- 获取数据：`getXxx()`, `findXxx()`, `queryXxx()`
- 保存数据：`saveXxx()`, `insertXxx()`, `updateXxx()`
- 删除数据：`deleteXxx()`, `removeXxx()`

## 变量命名规范
- 使用camelCase
- 布尔类型以`is`、`has`、`can`开头
- 常量使用全大写，下划线分隔

## 代码组织
- 每个类应该有明确的职责
- 使用接口定义服务契约
- 遵循SOLID原则
- 合理使用设计模式

## 异常处理
- 使用自定义业务异常：`ProviderTerminalBizException`
- 统一异常处理机制
- 记录详细的错误日志

## 数据库操作
- 使用MyBatis Plus进行数据访问
- DAO类以`DAO`结尾
- 实体类对应数据库表结构
description:
globs:
alwaysApply: false
---
