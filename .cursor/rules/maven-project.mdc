# Maven项目规范

## 项目结构
- 多模块Maven项目
- 父模块：`provider-terminal-service`
- 子模块：
  - `provider-terminal-service-api`: API定义模块
  - `provider-terminal-service-core`: 核心业务模块

## 依赖管理
- 使用父POM：`spring-boot-starter-parent-shouqianba`
- 版本：2.7.18-20241220
- Java版本：17
- 编码：UTF-8

## 仓库配置
- 私有仓库：`https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev`
- 快照仓库：同上
- 仓库ID：`central`和`snapshots`

## 构建配置
- 使用Maven编译插件
- 源代码编码：UTF-8
- 全局版本：1.0.0-SNAPSHOT

## 模块依赖关系
- core模块依赖api模块
- api模块不依赖其他模块
- 避免循环依赖

## 资源文件
- 配置文件放在`src/main/resources`目录
- 环境配置：`application-{env}.properties`
- 默认配置：`application.properties`

## 测试配置
- 测试代码放在`src/test/java`目录
- 测试资源放在`src/test/resources`目录
- 使用JUnit进行单元测试
description:
globs:
alwaysApply: false
---
