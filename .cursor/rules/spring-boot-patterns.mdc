# Spring Boot开发模式

## 配置管理
- 使用Apollo配置中心管理配置
- 配置类使用`@Configuration`注解
- 使用`@Value`或`@ConfigurationProperties`注入配置

## 控制器层
- 使用`@RestController`注解
- 路径映射使用`@RequestMapping`
- 返回统一响应格式：`ApiResponse<T>`
- 参数校验使用`@Valid`注解

## 服务层
- 使用`@Service`注解
- 接口和实现分离
- 事务管理使用`@Transactional`
- 异常处理统一在服务层

## 数据访问层
- 使用MyBatis Plus简化CRUD操作
- DAO类继承`BaseDAO<T>`
- 使用`@Mapper`注解标记接口
- 动态SQL使用`DynamicMapper`

## 外部服务调用
- 外部服务客户端放在`externalservice`包下
- 使用RPC框架进行服务间调用
- 配置RPC客户端使用`@RpcClient`注解

## 处理器模式
- 不同供应商的终端处理使用策略模式
- 处理器实现`ProviderTerminalHandler`接口
- 使用工厂模式创建处理器实例

## AOP切面
- 使用`@ProviderTerminalEntry`注解标记入口点
- 统一日志记录和性能监控
- 异常处理和事务管理

## 健康检查
- 提供健康检查端点：`/health`
- 监控指标端点：`/metrics`
- 应用状态监控
description:
globs:
alwaysApply: false
---
