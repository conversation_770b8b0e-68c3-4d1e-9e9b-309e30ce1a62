<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.shouqianba.cua</groupId>
    <artifactId>provider-terminal-service</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <modules>
        <module>provider-terminal-service-api</module>
        <module>provider-terminal-service-core</module>
    </modules>

    <parent>
        <groupId>com.shouqianba.middleware</groupId>
        <artifactId>spring-boot-starter-parent-shouqianba</artifactId>
        <version>2.7.18-20241220</version>
        <relativePath/>
    </parent>


    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <globalVersion>1.0.0-SNAPSHOT</globalVersion>
    </properties>


    <dependencyManagement>
        <dependencies>


        </dependencies>
    </dependencyManagement>


    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>
</project>